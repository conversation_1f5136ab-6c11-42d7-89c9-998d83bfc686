<template>
    <view class="container">
        <!-- 视频素材列表 -->
        <view class="media-list-container">
            <!-- 紧凑型视频列表项 -->
            <view v-for="(media, index) in filteredMedia" :key="index" class="media-item video-item"
                @click="viewMediaDetail(media)">

                <view class="media-thumbnail video-thumbnail">
                    <image :src="media.thumbnail" mode="aspectFill" class="media-thumbnail-image"></image>
                    <text class="video-duration">{{ media.duration }}</text>
                    <u-tag :text="getStatusText(media)" :type="getStatusType(media)" size="mini"
                        class="video-status"></u-tag>
                </view>

                <view class="media-content">
                    <view class="media-title">{{ media.title }}</view>
                    <view class="media-meta">
                        <text class="media-meta-item">{{ media.uploader }}</text>
                        <text class="media-meta-item">{{ formatDate(media.uploadTime) }}</text>
                    </view>
                </view>


            </view>

            <!-- 空状态 -->
            <u-empty v-if="filteredMedia.length === 0" mode="list" :text="`暂无${getStatusLabel(currentStatus)}视频`"
                iconSize="120" textSize="16" marginTop="100">
                <u-button type="primary" text="上传视频" @click="showUploadModal" size="normal" shape="round"></u-button>
            </u-empty>
        </view>

        <!-- 悬浮按钮组 -->
        <FloatingActionButton icon="/static/images/upload.png" text="上传" type="primary"
            :initialPosition="{ right: 20, bottom: 180 }" @click="showUploadModal" />

        <FloatingActionButton icon="/assets/images/upload.png" text="压缩" type="info"
            :initialPosition="{ right: 20, bottom: 120 }" @click="goToCompress" />
    </view>
</template>

<script>

import FloatingActionButton from '../../../components/FloatingActionButton.vue';
import { queryVideos } from "@/api/video.js";
import mediaCommonMixin from "@/mixins/media-common.js";

export default {
    mixins: [mediaCommonMixin],
    components: {
        FloatingActionButton
    },
    data () {
        return {
            mediaList: [],
            currentStatus: 'all'
        }
    },
    computed: {
        filteredMedia () {
            let result = this.mediaList;

            // 按状态筛选
            if (this.currentStatus !== 'all') {
                result = result.filter(media => media.status === this.currentStatus);
            }

            return result;
        }
    },
    created () {
        // 加载视频
        this.loadAllMedia();

        // 监听刷新事件
        uni.$on('refreshVideoList', () => {
            this.loadAllMedia();
        });
    },

    beforeDestroy () {
        // 移除事件监听
        uni.$off('refreshVideoList');
    },
    methods: {
        async loadAllMedia () {
            try {
                this.showLoading("加载中...");

                // 构建查询参数
                const params = {
                    PageIndex: 1,
                    PageSize: 100, // 暂时加载所有数据
                };

                // 如果有状态筛选，添加到查询参数
                if (this.currentStatus && this.currentStatus !== 'all') {
                    // 状态映射：active->1, scheduled->0, expired->2
                    const statusMap = {
                        'active': 1,
                        'scheduled': 0,
                        'expired': 2
                    };
                    params.Status = statusMap[this.currentStatus];
                }

                const response = await queryVideos(params);

                if (response.success && response.data) {
                    // 转换API数据格式为页面需要的格式
                    this.mediaList = response.data.items.map(video => ({
                        id: video.id,
                        title: video.title,
                        thumbnail: this.buildCompleteFileUrl(video.coverUrl) || '/assets/images/video-cover.jpg',
                        duration: this.formatDuration(video.duration),
                        uploadTime: this.formatDate(video.createTime),
                        uploader: video.creatorName || '未知',
                        uploaderId: video.creatorId || '',
                        views: video.viewCount || 0,
                        likes: video.likeCount || 0,
                        description: video.description || '',
                        status: video.status, // 直接使用API返回的状态值
                        videoUrl: this.buildCompleteFileUrl(video.videoUrl)
                    }));
                } else {
                    throw new Error(response.msg || '获取视频列表失败');
                }

                this.hideLoading();
            } catch (error) {
                console.error('加载视频列表失败:', error);
                this.hideLoading();
                this.showError("加载失败");
                this.mediaList = [];
            }
        },




        showUploadModal () {
            this.safeNavigateTo('/pages/admin/media/upload');
        },

        viewMediaDetail (media) {
            this.safeNavigateTo(`/pages/admin/media/detail?id=${media.id}`);
        },

        // 跳转到视频压缩页面
        goToCompress () {
            // this.safeNavigateTo('/pages/admin/media/video-compress'); // 此功能已移除
        },

        // 获取状态文本
        getStatusText (media) {
            const statusMap = {
                0: '已下架',
                1: '已上架',
                2: '处理失败',
                3: '压缩中'
            };
            return statusMap[media.status] || '未知状态';
        },

        // 获取状态类型
        getStatusType (media) {
            const typeMap = {
                0: 'warning',  // 下架 - 警告色
                1: 'success',  // 上架 - 成功色
                2: 'error',    // 失败 - 错误色
                3: 'info'      // 压缩中 - 信息色
            };
            return typeMap[media.status] || 'default';
        }

    }
}
</script>

<style lang="scss">
@import '@/styles/index.scss';

.container {
    @extend .page-container;
}

/* 视频列表容器 - 增大间距 */
.media-list-container {
    padding: $spacing-lg; // 从 $spacing-base 增大到 $spacing-lg
}

/* 视频列表项 - 恢复原始布局 */
.media-item.video-item {
    padding: $spacing-xl; // 从 $spacing-lg 增大到 $spacing-xl
    margin-bottom: $spacing-lg; // 从 $spacing-base 增大到 $spacing-lg
}

/* 视频特有样式 - 增大缩略图 */
.video-thumbnail {
    width: 240rpx; // 从 200rpx 增大到 240rpx
    height: 135rpx; // 从 112rpx 增大到 135rpx
}

/* 视频标题 - 增大字体 */
.media-title {
    font-size: $font-size-lg; // 从 $font-size-md 增大到 $font-size-lg
    margin-bottom: $spacing-base; // 从 $spacing-sm 增大到 $spacing-base
}

/* 视频元信息 - 增大字体 */
.media-meta {
    font-size: $font-size-base; // 从 $font-size-sm 增大到 $font-size-base
    line-height: 1.3; // 从 1.2 增大到 1.3
    display: inline-grid !important;
}

/* 视频时长标签 - 增大字体 */
.video-duration {
    position: absolute;
    bottom: 8rpx; // 从 6rpx 增大到 8rpx
    right: 8rpx; // 从 6rpx 增大到 8rpx
    background-color: rgba(0, 0, 0, 0.75);
    color: $text-white;
    font-size: $font-size-base; // 从 $font-size-sm 增大到 $font-size-base
    padding: 6rpx 12rpx; // 从 4rpx 8rpx 增大到 6rpx 12rpx
    border-radius: $border-radius-sm;
    line-height: 1.3; // 从 1.2 增大到 1.3
    font-weight: $font-weight-medium;
}

/* 视频状态标签 */
.video-status {
    position: absolute;
    top: 8rpx; // 从 6rpx 增大到 8rpx
    left: 8rpx; // 从 6rpx 增大到 8rpx
    transform: scale(1); // 从 scale(0.9) 改为 scale(1)
    transform-origin: top left;
}

/* 压缩中状态的脉动动画 */
.video-status[data-type="info"] {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
        transform: scale(1);
    }

    50% {
        opacity: 0.7;
        transform: scale(1.05);
    }

    100% {
        opacity: 1;
        transform: scale(1);
    }
}
</style>