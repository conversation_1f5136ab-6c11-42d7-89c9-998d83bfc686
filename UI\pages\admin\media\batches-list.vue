<template>
    <view class="container">
        <!-- 批次状态筛选 -->
        <view class="tabs-container">
            <!-- 使用 u-tabs 组件 -->
            <u-tabs v-if="false" :list="batchStatusTabs" :current="currentTabIndex" @change="onTabChange"
                :scrollable="false" lineColor="#007AFF" :activeStyle="{ color: '#007AFF' }"
                :inactiveStyle="{ color: '#666666' }"></u-tabs>

            <!-- 备用方案：自定义选项卡 -->
            <view class="custom-tabs">
                <view v-for="(tab, index) in batchStatusTabs" :key="index"
                    :class="['custom-tab', currentTabIndex === index ? 'active' : '']" @tap="onTabChange(index)">
                    {{ tab.name }}
                </view>
            </view>
        </view>

        <!-- 批次列表 -->
        <view class="media-list-container">
            <!-- 紧凑型批次列表项 -->
            <view v-for="(batch, index) in filteredBatches" :key="index" class="media-item batch-item"
                @click="viewBatchDetail(batch)">

                <!-- 批次封面缩略图 -->
                <view class="media-thumbnail batch-thumbnail">
                    <image v-if="batch.videos && batch.videos.length > 0 && batch.videos[0].cover"
                        :src="batch.videos[0].cover" mode="aspectFill" class="media-thumbnail-image"></image>
                    <view v-else class="no-cover-placeholder">
                        <u-icon name="photo" size="24" color="#ccc"></u-icon>
                    </view>
                    <u-tag :text="getBatchStatusText(batch)" :type="getBatchStatusType(batch)" size="mini"
                        class="batch-status"></u-tag>
                </view>

                <!-- 批次内容信息 -->
                <view class="media-content">
                    <view class="media-title batch-title">{{ batch.title }}</view>
                    <view class="batch-meta">
                        <text class="batch-id">{{ batch.batchId }}</text>
                        <text class="media-meta-separator">·</text>
                        <text class="batch-participants">{{ batch.participants }}人参与</text>
                        <text class="media-meta-separator">·</text>
                        <text class="batch-reward">{{ batch.totalReward }}元</text>
                    </view>
                    <view class="batch-date">
                        {{ formatDateRange(batch.startTime, batch.endTime) }}
                    </view>
                </view>


            </view>

            <!-- 空状态 -->
            <u-empty v-if="filteredBatches.length === 0" mode="list"
                :text="`暂无${getBatchStatusLabel(currentBatchStatus)}批次`" iconSize="120" textSize="16" marginTop="100">
            </u-empty>
        </view>
    </view>
</template>

<script>

import { queryBatches } from "@/api/batch.js";
import mediaCommonMixin from "@/mixins/media-common.js";

export default {
    mixins: [mediaCommonMixin],
    data () {
        return {
            batchList: [],
            batchStatusTabs: [
                { name: '全部', value: 'all' },
                { name: '进行中', value: 'active' },
                { name: '未开始', value: 'pending' },
                { name: '已结束', value: 'ended' }
            ],
            currentBatchStatus: 'all',
            currentTabIndex: 0
        }
    },
    computed: {
        filteredBatches () {
            let result = this.batchList;

            // 按状态筛选
            if (this.currentBatchStatus !== 'all') {
                result = result.filter(batch => batch.status === this.currentBatchStatus);
            }

            return result;
        }
    },
    created () {
        this.loadAllBatches();
    },
    methods: {
        async loadAllBatches () {
            try {
                uni.showLoading({
                    title: "加载中...",
                });

                // 构建查询参数
                const params = {
                    PageIndex: 1,
                    PageSize: 100, // 暂时加载所有数据
                };

                // 如果有状态筛选，添加到查询参数
                if (this.currentBatchStatus && this.currentBatchStatus !== 'all') {
                    // 状态映射：active->1, pending->0, ended->2
                    const statusMap = {
                        'active': 1,
                        'pending': 0,
                        'ended': 2
                    };
                    params.Status = statusMap[this.currentBatchStatus];
                }

                const response = await queryBatches(params);

                if (response.success && response.data) {
                    // 转换API数据格式为页面需要的格式
                    this.batchList = response.data.items.map(batch => ({
                        id: batch.id,
                        batchId: `B${batch.id}`,
                        title: batch.name || batch.title,
                        status: this.mapBatchStatus(batch.status),
                        createTime: this.formatDate(batch.createTime),
                        startTime: this.formatDate(batch.startTime),
                        endTime: this.formatDate(batch.endTime),
                        creator: batch.creatorName || '未知',
                        totalViews: batch.currentParticipants || 0,
                        participants: batch.currentParticipants || 0,
                        totalReward: batch.rewardAmount || 0,
                        redPacketAmount: batch.redPacketAmount || 0,
                        videoCount: 1, // 新接口中一个批次对应一个视频
                        description: batch.description || '',
                        // 视频信息
                        videoId: batch.videoId,
                        videoTitle: batch.videoTitle,
                        videoCoverUrl: batch.videoCoverUrl,
                        // 使用实际视频数据而不是模拟数据
                        videos: [{
                            id: batch.videoId,
                            title: batch.videoTitle,
                            cover: this.buildCompleteFileUrl(batch.videoCoverUrl),
                            duration: batch.videoDuration
                        }]
                    }));
                } else {
                    throw new Error(response.msg || '获取批次列表失败');
                }

                uni.hideLoading();
            } catch (error) {
                uni.hideLoading();
                uni.showToast({
                    title: "加载失败",
                    icon: "none",
                });
            }
        },

        // 映射批次状态
        mapBatchStatus (apiStatus) {
            const statusMap = {
                0: 'pending', // 草稿/待开始
                1: 'active',  // 进行中
                2: 'ended',   // 已结束
                3: 'paused'   // 已暂停
            };
            return statusMap[apiStatus] || 'pending';
        },

        // 格式化日期
        formatDate (dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`;
        },

        // 格式化日期范围
        formatDateRange (startTime, endTime) {
            if (!startTime || !endTime) return '';
            const start = new Date(startTime);
            const end = new Date(endTime);
            const startStr = `${start.getFullYear()}-${(start.getMonth() + 1).toString().padStart(2, '0')}-${start.getDate().toString().padStart(2, '0')} ${start.getHours().toString().padStart(2, '0')}:${start.getMinutes().toString().padStart(2, '0')}`;
            const endStr = `${end.getFullYear()}-${(end.getMonth() + 1).toString().padStart(2, '0')}-${end.getDate().toString().padStart(2, '0')} ${end.getHours().toString().padStart(2, '0')}:${end.getMinutes().toString().padStart(2, '0')}`;
            return `${startStr} ~ ${endStr}`;
        },


        // u-tabs 组件的切换事件
        onTabChange (index) {
            this.currentTabIndex = index;
            this.currentBatchStatus = this.batchStatusTabs[index].value;
            // 状态改变时重新加载数据
            this.loadAllBatches();
        },

        getBatchStatusLabel (status) {
            const tab = this.batchStatusTabs.find(tab => tab.value === status);
            return tab ? tab.name : '';
        },
        // 获取批次状态对应的 uview-plus 标签类型
        getBatchStatusType (batch) {
            // 首先检查是否已过期
            const now = new Date();
            const endTime = new Date(batch.endTime);
            const startTime = new Date(batch.startTime);

            if (now > endTime) {
                return 'error'; // 已结束
            }

            if (now < startTime) {
                return 'warning'; // 未开始
            }

            // 其他状态
            if (batch.status === 'paused') return 'warning';
            return 'success'; // 进行中
        },

        viewBatchDetail(batch) {
            if (!batch || !batch.id) {
                console.error('无效的批次数据，无法跳转');
                uni.showToast({
                    title: '无效的批次数据',
                    icon: 'none'
                });
                return;
            }
            const url = `/pages/admin/media/batch-detail?id=${batch.id}`;
            uni.navigateTo({
                url: url,
                fail: (err) => {
                    console.error('跳转到批次详情页失败:', err);
                    uni.showToast({
                        title: '页面跳转失败',
                        icon: 'none'
                    });
                }
            });
        },
        // 添加批次暂停/恢复功能
        toggleBatchStatus (batch) {
            const action = batch.status === 'active' ? '暂停' : '恢复';
            uni.showModal({
                title: `确认${action}`,
                content: `确定要${action}批次"${batch.title}"吗？`,
                success: (res) => {
                    if (res.confirm) {
                        // 模拟操作
                        const index = this.batchList.findIndex(item => item.id === batch.id);
                        if (index !== -1) {
                            // 切换状态
                            if (this.batchList[index].status === 'active') {
                                this.batchList[index].status = 'paused';
                            } else if (this.batchList[index].status === 'paused') {
                                this.batchList[index].status = 'active';
                            }

                            uni.showToast({
                                title: `${action}成功`,
                                icon: 'success'
                            });
                        }
                    }
                }
            });
        },
        getBatchStatusText (batch) {
            // 首先检查是否已过期
            const now = new Date();
            const endTime = new Date(batch.endTime);
            const startTime = new Date(batch.startTime);

            if (now > endTime) {
                return '已结束';
            }

            if (now < startTime) {
                return '未开始';
            }

            // 其他状态
            if (batch.status === 'paused') return '已暂停';
            return '进行中';
        },
        // 检查批次是否已结束
        isBatchEnded (batch) {
            const now = new Date();
            const endTime = new Date(batch.endTime);
            return now > endTime;
        },


    }
}
</script>

<style lang="scss">
@import '@/styles/index.scss';

.container {
    @extend .page-container;
}

/* 选项卡容器 */
.tabs-container {
    background-color: $bg-primary;
    padding: $spacing-lg;
    margin-bottom: $spacing-base;
    box-shadow: $shadow-sm;
    min-height: 88rpx;
    display: flex;
    align-items: center;
}

/* 自定义选项卡样式 */
.custom-tabs {
    display: flex;
    width: 100%;
}

.custom-tab {
    flex: 1;
    text-align: center;
    padding: $spacing-lg 0;
    font-size: $font-size-base;
    color: $text-secondary;
    border-bottom: 4rpx solid transparent;
    transition: all $transition-base ease;
}

.custom-tab.active {
    color: $primary-color;
    border-bottom-color: $primary-color;
    font-weight: $font-weight-medium;
}

.custom-tab:active {
    opacity: 0.7;
}

/* 批次列表容器 - 增大间距 */
.media-list-container {
    padding: $spacing-lg; // 从 $spacing-base 增大到 $spacing-lg
}

/* 批次列表项 - 增大内边距和字体 */
.media-item.batch-item {
    padding: $spacing-xl; // 从 $spacing-lg 增大到 $spacing-xl
    margin-bottom: $spacing-lg; // 从 $spacing-base 增大到 $spacing-lg
}

/* 批次特有样式 - 增大缩略图 */
.batch-thumbnail {
    width: 240rpx; // 从 200rpx 增大到 240rpx
    height: 135rpx; // 从 112rpx 增大到 135rpx
}

/* 无封面占位符 */
.no-cover-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: $bg-tertiary;
    border-radius: $border-radius-sm;
}

/* 批次状态标签 */
.batch-status {
    position: absolute;
    top: 8rpx; // 从 6rpx 增大到 8rpx
    left: 8rpx; // 从 6rpx 增大到 8rpx
    transform: scale(1); // 从 scale(0.9) 改为 scale(1)
    transform-origin: top left;
}

/* 批次标题 - 增大字体 */
.media-title.batch-title {
    font-size: $font-size-lg; // 从 $font-size-md 增大到 $font-size-lg
    margin-bottom: $spacing-base; // 从 $spacing-sm 增大到 $spacing-base
}

/* 批次元信息 - 增大字体 */
.batch-meta {
    display: flex;
    align-items: center;
    font-size: $font-size-base; // 从 $font-size-sm 增大到 $font-size-base
    color: $text-tertiary;
    line-height: 1.3; // 从 1.2 增大到 1.3
    margin-bottom: $spacing-sm; // 从 $spacing-xs 增大到 $spacing-sm
}

.batch-id {
    color: $text-secondary;
    font-weight: $font-weight-medium;
}

.batch-participants {
    color: $primary-color;
    font-weight: $font-weight-medium;
}

.batch-reward {
    color: $warning-color;
    font-weight: $font-weight-medium;
}

/* 批次日期 - 增大字体 */
.batch-date {
    font-size: $font-size-sm; // 从 $font-size-xs 增大到 $font-size-sm
    color: $text-disabled;
    line-height: 1.3; // 从 1.2 增大到 1.3
}
</style>