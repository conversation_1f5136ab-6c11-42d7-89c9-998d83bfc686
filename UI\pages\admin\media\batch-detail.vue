<template>
    <view class="page-container">


        <!-- 主要内容区域 -->
        <view class="main-layout">
            <!-- 左侧主内容 -->
            <view class="content-main">
                <!-- 视频播放区域 -->
                <view class="video-card" v-if="batchVideos.length > 0">
                    <view class="video-player-container">
                        <video :src="batchVideos[0].videoUrl || '/assets/videos/sample.mp4'"
                            :poster="batchVideos[0].thumbnail" controls class="video-player" @error="handleVideoError">
                        </video>
                        <view class="video-overlay" v-if="!batchVideos[0].videoUrl">
                            <text class="overlay-text">视频加载中...</text>
                        </view>
                    </view>

                    <!-- 视频信息 -->
                    <view class="video-info">
                        <view class="video-header">
                            <text class="video-title">{{ batchVideos[0].title }}</text>
                        </view>
                        <view class="video-description" v-if="batchVideos[0].description">
                            <text class="description-icon">ℹ️</text>
                            <text class="description-text">{{ batchVideos[0].description }}</text>
                        </view>

                        <!-- 链接信息展示 -->
                        <view class="link-info-section">
                            <view class="link-info-header">
                                <text class="link-icon">�</text>
                                <text class="link-title">分享链接</text>
                            </view>
                            <view class="link-url-display">
                                <text class="link-url">{{ getVideoShareUrl() }}</text>
                            </view>
                        </view>

                        <!-- 操作按钮 -->
                        <view class="action-buttons">
                            <view class="button-row">
                                <button class="action-btn copy-link-btn" @tap="copyVideoLink">
                                    <text class="btn-text">复制链接</text>
                                </button>
                                <button class="action-btn view-data-btn" @tap="viewBatchData">
                                    <text class="btn-text">查看数据</text>
                                </button>
                            </view>
                            <view class="button-row full-width">
                                <button class="delete-btn danger" @tap="showDeleteConfirm">
                                    <text class="btn-text">删除批次</text>
                                </button>
                            </view>
                        </view>


                    </view>
                </view>


            </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" v-if="batchVideos.length === 0">
            <view class="empty-icon">
                <text class="iconfont icon-video-off"></text>
            </view>
            <text class="empty-title">该批次暂无视频</text>
            <text class="empty-desc">请添加视频内容或选择其他批次</text>
        </view>
    </view>

    <!-- 删除确认弹窗 -->
    <view v-if="showDeleteDialog" class="modal-overlay">
        <view class="confirm-modal">
            <view class="modal-header">
                <text class="modal-title">确认删除</text>
            </view>
            <view class="modal-body">
                <text class="confirm-text">确定要删除批次"{{ batch.title }}"吗？删除后无法恢复，请谨慎操作。</text>
            </view>
            <view class="modal-footer">
                <button class="btn secondary" @tap="hideDeleteConfirm">取消</button>
                <button class="btn danger" @tap="deleteBatch">确认删除</button>
            </view>
        </view>
    </view>
</template>

<script>
import { getBatchDetail } from "@/api/batch.js";
import authService from "@/utils/auth.js";
import { getUIProjectUrl } from "@/utils/app-config.js";
import mediaCommonMixin from "@/mixins/media-common.js";

export default {
    mixins: [mediaCommonMixin],
    data () {
        return {
            batchId: null, // 从URL获取
            batch: null, // 从API加载
            batchVideos: [],
            showActionMenu: false,
            showDeleteDialog: false
        }
    },

    onLoad (options) {
        if (options.id) {
            this.batchId = options.id;
            this.fetchBatchDetails();
        } else {
            uni.showToast({
                title: '无效的批次ID',
                icon: 'none',
                duration: 2000
            });
            // 1.5秒后返回上一页
            setTimeout(() => uni.navigateBack(), 1500);
        }
    },
    methods: {
        async fetchBatchDetails () {
            try {
                uni.showLoading({
                    title: "加载中...",
                });

                // 调用API获取批次详情
                const response = await getBatchDetail(this.batchId);

                if (response.success && response.data) {
                    const batch = response.data;
                    this.batch = {
                        id: batch.id,
                        batchId: `B${batch.id}`,
                        title: batch.name || batch.title,
                        status: this.mapBatchStatus(batch.status),
                        createTime: this.formatDate(batch.createTime),
                        startTime: this.formatDate(batch.startTime),
                        endTime: this.formatDate(batch.endTime),
                        creator: batch.creatorName || '未知',
                        videoCount: 1, // 新接口中一个批次对应一个视频
                        totalViews: batch.currentParticipants || 0,
                        participants: batch.currentParticipants || 0,
                        totalReward: batch.rewardAmount || 0,
                        redPacketAmount: batch.redPacketAmount || 0,
                        description: batch.description || '',
                        // 视频相关信息
                        videoId: batch.videoId,
                        videoTitle: batch.videoTitle,
                        videoDescription: batch.videoDescription,
                        videoCoverUrl: batch.videoCoverUrl,
                        videoUrl: batch.videoUrl,
                        videoDuration: batch.videoDuration,
                        // 题目信息
                        questions: batch.questions || [],
                        // 统计信息
                        statistics: batch.statistics || {}
                    };

                    // 数据加载成功后，构造视频信息
                    this.loadBatchVideos();
                } else {
                    throw new Error(response.msg || '获取批次详情失败');
                }

                uni.hideLoading();
            } catch (error) {
                uni.hideLoading();
                uni.showToast({
                    title: "加载失败",
                    icon: "none",
                });
            }
        },

        // 映射批次状态
        mapBatchStatus (apiStatus) {
            const statusMap = {
                0: 'pending', // 草稿/待开始
                1: 'active',  // 进行中
                2: 'ended',   // 已结束
                3: 'paused'   // 已暂停
            };
            return statusMap[apiStatus] || 'pending';
        },

        // 格式化日期
        formatDate (dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`;
        },

        // 格式化时长
        formatDuration (seconds) {
            if (!seconds || seconds === 0) return '未知';
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;

            if (hours > 0) {
                return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            } else {
                return `${minutes}:${secs.toString().padStart(2, '0')}`;
            }
        },


        loadBatchVideos () {
            // 从批次详情中构造视频数据
            if (this.batch.videoId) {
                this.batchVideos = [{
                    id: this.batch.videoId,
                    title: this.batch.videoTitle || this.batch.title || '该批次无视频',
                    description: this.batch.videoDescription || '',
                    videoUrl: this.buildCompleteFileUrl(this.batch.videoUrl),
                    thumbnail: this.buildCompleteFileUrl(this.batch.videoCoverUrl) || '/assets/images/video-cover.jpg',
                    duration: this.batch.videoDuration || 0,
                    // 添加一些默认的统计数据
                    views: this.batch.totalViews || 0,
                    likes: 0,
                    comments: 0
                }];

                console.log('构造的视频数据:', this.batchVideos[0]);
            } else {
                this.batchVideos = [];
                console.log('批次中没有视频数据');
            }
        },
        goBack () {
            uni.navigateBack();
        },
        viewRealTimeData () {
            uni.navigateTo({
                url: `/pages/admin/media/batch-realtime?id=${this.batchId}`
            });
        },
        showAddVideoModal () {
            // 添加视频的逻辑
            uni.showToast({
                title: '添加视频功能开发中',
                icon: 'none'
            });
        },
        viewVideoDetail (video) {
            uni.navigateTo({
                url: `/pages/admin/media/detail?id=${video.id}`
            });
        },
        showVideoMenu (video) {
            // 显示视频操作菜单
            uni.showActionSheet({
                itemList: ['移出批次', '查看详情'],
                success: (res) => {
                    if (res.tapIndex === 0) {
                        // 移出批次
                        uni.showToast({
                            title: '移出批次功能开发中',
                            icon: 'none'
                        });
                    } else if (res.tapIndex === 1) {
                        // 查看详情
                        this.viewVideoDetail(video);
                    }
                }
            });
        },

        getBatchStatusClass (batch) {
            if (batch.status === 'ended') return 'status-expired';
            if (batch.status === 'pending') return 'status-scheduled';
            return 'status-active';
        },
        getBatchStatusText (batch) {
            if (batch.status === 'ended') return '已结束';
            if (batch.status === 'pending') return '未开始';
            return '进行中';
        },
        // 处理视频播放错误
        handleVideoError (e) {
            console.error('视频播放错误:', e.detail);

            // 显示更详细的错误信息
            let errorMsg = '视频加载失败';
            if (e.detail && e.detail.errMsg) {
                errorMsg += `: ${e.detail.errMsg}`;
            }

            // 检查当前视频URL
            if (this.batchVideos.length > 0) {
                console.log('视频URL:', this.batchVideos[0].videoUrl);

                // 如果没有视频URL，尝试使用备用视频
                if (!this.batchVideos[0].videoUrl) {
                    this.batchVideos[0].videoUrl = '/static/videos/sample.mp4';
                    console.log('使用备用视频URL:', this.batchVideos[0].videoUrl);

                    // 提示用户
                    errorMsg = '原视频缺失，已替换为示例视频';
                }
            }

            uni.showToast({
                title: errorMsg,
                icon: 'none',
                duration: 3000
            });
        },

        // 获取视频分享链接
        getVideoShareUrl () {
            try {
                // 获取当前登录用户信息
                const loginInfo = authService.getLoginInfo();
                if (!loginInfo || !loginInfo.userId) {
                    return '获取链接失败：用户信息缺失';
                }

                // 检查批次信息
                if (!this.batch || !this.batch.videoId || !this.batchId) {
                    return '获取链接失败：批次信息不完整';
                }

                // 获取UI项目访问地址
                const UIProjectUrl = getUIProjectUrl();
                console.log('当前配置的UIProjectUrl:', UIProjectUrl);
                console.log('window.APP_CONFIG:', window.APP_CONFIG);

                // 构建完整的视频页面URL，包含分享人ID（使用hash路由模式）
                return `${UIProjectUrl}/#/pages/video/index?videoId=${this.batch.videoId}&batchId=${this.batchId}&sharerId=${loginInfo.userId}`;
            } catch (error) {
                console.error('获取分享链接失败:', error);
                // 显示具体的错误信息
                this.$toast(error.message || '获取链接失败');
                return `配置错误：${error.message}`;
            }
        },

        // 复制视频链接
        copyVideoLink () {
            try {
                const videoUrl = this.getVideoShareUrl();

                if (videoUrl.includes('获取链接失败')) {
                    uni.showToast({
                        title: videoUrl,
                        icon: 'none',
                        duration: 3000
                    });
                    return;
                }

                // 显示复制中提示
                uni.showLoading({
                    title: '复制中...'
                });

                // 复制到剪贴板
                uni.setClipboardData({
                    data: videoUrl,
                    success: () => {
                        uni.hideLoading();
                        uni.showToast({
                            title: '分享链接已复制到剪贴板',
                            icon: 'success',
                            duration: 2000
                        });
                        console.log('分享链接已复制:', videoUrl);
                    },
                    fail: (error) => {
                        uni.hideLoading();
                        console.error('复制到剪贴板失败:', error);
                        uni.showToast({
                            title: '复制失败，请手动复制链接',
                            icon: 'none',
                            duration: 3000
                        });
                    }
                });
            } catch (error) {
                uni.hideLoading();
                console.error('复制链接失败:', error);
                uni.showToast({
                    title: '复制失败，请重试',
                    icon: 'none',
                    duration: 3000
                });
            }
        },

        // 查看批次数据
        viewBatchData () {
            uni.navigateTo({
                url: `/pages/admin/media/batch-data?id=${this.batchId}`
            });
        },

        // 显示操作菜单
        showActionMenuHandler () {
            this.showActionMenu = true;
        },

        // 隐藏操作菜单
        hideActionMenu () {
            this.showActionMenu = false;
        },





        // 获取视频问题数量
        getQuizCount () {
            // 如果批次中有问题数据，返回问题数量
            if (this.batch.questions && Array.isArray(this.batch.questions)) {
                return this.batch.questions.length;
            }
            return 0;
        },


        // 显示删除确认对话框
        showDeleteConfirm () {
            this.hideActionMenu();
            this.showDeleteDialog = true;
        },

        // 隐藏删除确认对话框
        hideDeleteConfirm () {
            this.showDeleteDialog = false;
        },

        deleteBatch () {
            this.hideDeleteConfirm();
            // 这里实际项目中应该调用API删除批次
            uni.showToast({
                title: '批次已删除',
                icon: 'success'
            });

            // 删除成功后返回上一页
            setTimeout(() => {
                uni.navigateBack();
            }, 1500);
        }
    }
}
</script>

<style lang="scss">
@import '@/styles/index.scss';

/* === 页面容器 === */
.page-container {
    background-color: #f8faff;
    display: flex;
    flex-direction: column;
    padding-bottom: 40rpx;
}




.action-btn {
    display: flex;
    align-items: center;
    gap: $spacing-xs;
    padding: $spacing-sm $spacing-base;
    border-radius: 10rpx !important;
    border: 1rpx solid $border-light;
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
    transition: all $transition-base;
    min-height: 64rpx;
    background-color: $bg-primary;
    color: $text-primary;

    &:hover {
        border-color: $border-secondary;
        background-color: $bg-tertiary;
    }

    &:active {
        background-color: $bg-secondary;
        transform: translateY(1rpx);
    }



    &.secondary {
        border-color: $border-light;
        background-color: #186BFF;
        color: white;

        &:hover {
            border-color: $border-secondary;
            background-color: #1565c0;
            color: white;
        }

        &:active {
            background-color: #0d47a1;
            transform: translateY(1rpx);
        }
    }

    .btn-icon {
        font-size: 28rpx;
    }

    .btn-text {
        font-size: $font-size-sm;
    }
}

/* === 主布局 === */
.main-layout {
    flex: 1;
    display: flex;
    gap: $spacing-xl;
    max-width: 1200rpx;
    margin: 0 auto;
    width: 100%;
    box-sizing: border-box;

    @media (max-width: 768px) {
        flex-direction: column;
        gap: $spacing-lg;
    }
}

.content-main {
    flex: 2;
    min-width: 0;
}



/* === 视频卡片 === */
.video-card {
    background-color: $bg-primary;
    border-radius: $border-radius-lg;
    overflow: hidden;
    //box-shadow: $shadow-base;
    // border: 1rpx solid $border-light;
    transition: all $transition-base;

    // &:hover {
    //     box-shadow: $shadow-lg;
    //     transform: translateY(-2rpx);
    // }
}

.video-player-container {
    position: relative;
    width: 100%;
    aspect-ratio: 16/9;
    background-color: #000;
    overflow: hidden;
}

.video-player {
    width: 100%;
    height: 100%;
    display: block;
    object-fit: contain;
    background-color: #000;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    color: $text-white;
    font-size: $font-size-base;
}

.video-info {
    padding: $spacing-xl;
}

/* === 视频标题和头部 === */
.video-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: $spacing-base;
    margin-bottom: $spacing-lg;
    position: relative;
}

.video-title {
    font-size: $font-size-xl;
    font-weight: $font-weight-bold;
    color: $text-primary;
    line-height: 1.3;
    word-break: break-word;
    flex: 1;
}

/* === 删除按钮 === */
.delete-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: $spacing-xs;
    width: 100%;
    padding: $spacing-sm $spacing-base;
    border-radius: 10rpx !important;
    border: 1rpx solid $error-color;
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
    transition: all $transition-base;
    min-height: 64rpx;
    background-color: $error-color;
    color: #fff !important;

    &:hover {
        border-color: #e6393f;
        background-color: #e6393f;
    }

    &:active {
        border-color: #d32f2f;
        background-color: #d32f2f;
        transform: translateY(1rpx);
    }

    .btn-icon {
        font-size: 28rpx;
    }

    .btn-text {
        font-size: $font-size-sm !important;
        color: #fff !important;
        font-weight: $font-weight-medium !important;
    }
}

/* === 视频描述 === */
.video-description {
    display: flex;
    align-items: flex-start;
    gap: $spacing-base;
    padding: $spacing-lg;
    background: linear-gradient(135deg, rgba(24, 107, 255, 0.05) 0%, rgba(24, 107, 255, 0.02) 100%);
    border-radius: $border-radius-base;
    border-left: 4rpx solid #186BFF;
    margin-bottom: $spacing-lg;
}

.description-icon {
    font-size: 32rpx;
    color: #186BFF;
    margin-top: 4rpx;
    flex-shrink: 0;
}

.description-text {
    font-size: $font-size-base;
    color: $text-secondary;
    line-height: $line-height-relaxed;
    flex: 1;
}

/* === 链接信息展示 === */
.link-info-section {
    padding: $spacing-lg;
    background: linear-gradient(135deg, rgba(24, 107, 255, 0.05) 0%, rgba(24, 107, 255, 0.02) 100%);
    border-radius: $border-radius-base;
    border-left: 4rpx solid #186BFF;
    margin-bottom: $spacing-lg;
}

.link-info-header {
    display: flex;
    align-items: center;
    gap: $spacing-base;
    margin-bottom: $spacing-base;
}

.link-icon {
    font-size: 32rpx;
    color: #186BFF;
}

.link-title {
    font-size: $font-size-base;
    font-weight: $font-weight-semibold;
    color: $text-primary;
}

.link-url-display {
    background-color: $bg-primary;
    border: 1rpx solid $border-light;
    border-radius: $border-radius-sm;
    padding: $spacing-base;
}

.link-url {
    font-size: $font-size-sm;
    color: $text-secondary;
    word-break: break-all;
    line-height: $line-height-relaxed;
}

/* === 操作按钮 === */
.action-buttons {
    display: flex;
    flex-direction: column;
    gap: $spacing-base;
    padding: 0 !important;
}

.button-row {
    display: flex;
    gap: $spacing-base;
    width: 100%;

    &.full-width {
        margin-top: $spacing-base;
    }
}

.copy-link-btn {
    flex: 1;
    background-color: #186BFF !important;
    border-color: #186BFF !important;
    color: $text-white !important;
    font-size: $font-size-base !important;
    padding: $spacing-lg $spacing-xl !important;
    min-height: 80rpx !important;

    &:hover {
        background-color: #1565c0 !important;
        border-color: #1565c0 !important;
    }

    &:active {
        background-color: #0d47a1 !important;
        border-color: #0d47a1 !important;
        transform: translateY(1rpx);
    }

    .btn-icon {
        font-size: 32rpx !important;
        color: $text-white !important;
    }

    .btn-text {
        font-size: $font-size-base !important;
        color: $text-white !important;
        font-weight: $font-weight-medium !important;
    }
}

.view-data-btn {
    flex: 1;
    background-color: white !important;
    border: 2rpx solid #186BFF !important;
    color: #186BFF !important;
    font-size: $font-size-base !important;
    padding: $spacing-lg $spacing-xl !important;
    min-height: 80rpx !important;

    &:hover {
        background-color: #f0f9ff !important;
        border-color: #1565c0 !important;
        color: #1565c0 !important;
    }

    &:active {
        background-color: #e6f7ff !important;
        border-color: #0d47a1 !important;
        color: #0d47a1 !important;
        transform: translateY(1rpx);
    }

    .btn-icon {
        font-size: 32rpx !important;
        color: #186BFF !important;
    }

    .btn-text {
        font-size: $font-size-base !important;
        color: #186BFF !important;
        font-weight: $font-weight-medium !important;
    }
}





/* === 空状态 === */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $spacing-xxxl;
    background-color: $bg-primary;
    border-radius: $border-radius-lg;
    border: 1rpx solid $border-light;
    text-align: center;
}

.empty-icon {
    width: 120rpx;
    height: 120rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: $bg-tertiary;
    border-radius: $border-radius-round;
    margin-bottom: $spacing-lg;

    .iconfont {
        font-size: 60rpx;
        color: $text-tertiary;
    }
}

.empty-title {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-primary;
    margin-bottom: $spacing-base;
}

.empty-desc {
    font-size: $font-size-base;
    color: $text-tertiary;
    line-height: $line-height-relaxed;
}

/* === 侧边栏卡片 === */
.sidebar-card {
    background-color: $bg-primary;
    // border-radius: $border-radius-lg;
    // border: 1rpx solid $border-light;
    // box-shadow: $shadow-sm;
    overflow: hidden;
    transition: all $transition-base;

    &:hover {
        box-shadow: $shadow-base;
        transform: translateY(-1rpx);
    }
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: $spacing-lg $spacing-xl;
    border-bottom: 1rpx solid $border-light;
    background: linear-gradient(135deg, rgba($primary-color, 0.02) 0%, transparent 100%);
}

.card-title {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-primary;
}

.status-badge {
    padding: $spacing-xs $spacing-base;
    border-radius: $border-radius-xs;
    font-size: $font-size-xs;
    font-weight: $font-weight-medium;

    &.status-active {
        background-color: rgba($success-color, 0.1);
        color: $success-color;
        border: 1rpx solid rgba($success-color, 0.2);
    }

    &.status-expired {
        background-color: rgba($error-color, 0.1);
        color: $error-color;
        border: 1rpx solid rgba($error-color, 0.2);
    }

    &.status-scheduled {
        background-color: rgba($warning-color, 0.1);
        color: $warning-color;
        border: 1rpx solid rgba($warning-color, 0.2);
    }
}

.status-text {
    font-size: $font-size-xs;
}

.card-content {
    padding: $spacing-xl;
}





/* === 描述内容 === */
.description-content {
    font-size: $font-size-base;
    color: $text-secondary;
    line-height: $line-height-relaxed;
}





/* === 按钮图标 === */
.btn-icon {
    font-size: 32rpx;
    margin-right: $spacing-xs;
}

/* === 模态框 === */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: $z-index-modal;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: $spacing-xl;
    animation: fadeIn $transition-base ease-out;
}

.action-modal,
.confirm-modal {
    background-color: $bg-primary;
    border-radius: $border-radius-lg;
    box-shadow: $shadow-lg;
    max-width: 500rpx;
    width: 100%;
    animation: slideInUp $transition-base ease-out;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: $spacing-xl $spacing-xl $spacing-lg $spacing-xl;
    border-bottom: 1rpx solid $border-light;
}

.modal-title {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-primary;
}

.close-btn {
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: $border-radius-round;
    background-color: $bg-tertiary;
    border: none;
    transition: all $transition-base;

    &:active {
        background-color: $bg-secondary;
        transform: scale(0.95);
    }

    .iconfont {
        font-size: 24rpx;
        color: $text-secondary;
    }
}

.modal-content {
    padding: $spacing-xl;
}

.modal-body {
    padding: $spacing-xl;
}

.modal-footer {
    display: flex;
    gap: $spacing-base;
    padding: $spacing-lg $spacing-xl $spacing-xl $spacing-xl;
    border-top: 1rpx solid $border-light;
}

.confirm-text {
    font-size: $font-size-base;
    color: $text-secondary;
    line-height: $line-height-relaxed;
}

.btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: $spacing-lg;
    border-radius: $border-radius-base;
    border: none;
    font-size: $font-size-base;
    font-weight: $font-weight-medium;
    transition: all $transition-base;

    &.secondary {
        background-color: $bg-tertiary;
        color: $text-primary;
        border: 1rpx solid $border-secondary;

        &:active {
            background-color: $bg-secondary;
        }
    }

    &.danger {
        background-color: $error-color;
        color: $text-white;

        &:active {
            background-color: #e6393f;
        }
    }
}

/* === 动画 === */
@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50rpx);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}



/* === 响应式设计 === */
@media (max-width: 768px) {
    .video-header {
        flex-direction: column;
        align-items: flex-start;
        gap: $spacing-base;
    }

    .video-info {
        padding: $spacing-lg;
    }

    .modal-overlay {
        padding: $spacing-lg;
    }
}
</style>