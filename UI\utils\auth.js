/**
 * Authentication utility for the video quiz system
 * Handles login, logout, and user session management
 */

// 导入API模块
import authApi from '../api/auth.js'
import CryptoJS from 'crypto-js'

// User types - 映射数字类型到字符串
// 根据数据库表结构：1:超级管理员 2:管理员 3:员工
export const USER_TYPES = {
  1: 'admin',     // 超管
  2: 'manager',   // 管理
  3: 'employee',  // 员工
  'ip_user': 'ip_user',  // IP用户
  'user': 'user'  // 普通用户（兼容）
}

// 反向映射
export const USER_TYPE_NAMES = {
  1: '超管',
  2: '管理',
  3: '员工'
}

// Storage keys
const STORAGE_KEYS = {
  LOGIN_INFO: 'loginInfo',
  ADMIN_LOGIN_INFO: 'adminLoginInfo', // For backward compatibility
  USER_PERMISSIONS: 'userPermissions'
}

/**
 * Authentication service class
 */
class AuthService {

  /**
   * Authenticate user with username and password
   * @param {string} username
   * @param {string} password
   * @returns {Promise<Object>} Authentication result
   */
  async authenticate (username, password) {
    try {
      // 对密码进行MD5加密
      const hashedPassword = CryptoJS.MD5(password).toString()

      // 调用真实的登录API
      const response = await authApi.login({ username, password: hashedPassword })

      if (response && response.success && response.data && response.data.accessToken) {
        // 获取用户信息
        const userInfo = response.data.userInfo || {}

        // Create login session
        const userType = USER_TYPES[userInfo.userType] || 'employee'

        // 根据用户类型设置权限
        let permissions = []
        if (userType === 'admin') {
          permissions = ['*'] // 超管拥有所有权限
        } else if (userType === 'manager') {
          permissions = ['view_dashboard', 'manage_users', 'view_reports', 'view_videos', 'take_quiz']
        } else {
          permissions = ['view_videos', 'take_quiz', 'view_rewards']
        }

        const loginInfo = {
          username: userInfo.username || username,
          userId: userInfo.userId,
          nickName: userInfo.nickName,
          userType: userType,
          userTypeCode: userInfo.userType,
          accessToken: response.data.accessToken,
          permissions: permissions,
          loginTime: new Date().getTime()
        }

        // Save login info
        this.saveLoginInfo(loginInfo)

        return {
          success: true,
          message: '登录成功',
          user: loginInfo
        }
      } else {
        // 检查是否是API返回的错误信息
        const errorMessage = (response && response.msg) || '登录失败，请检查用户名和密码'
        return {
          success: false,
          message: errorMessage
        }
      }

    } catch (error) {
      console.error('Authentication error:', error)

      // 如果是网络错误或其他错误，返回错误信息
      const errorMessage = error.message || '登录失败，请重试'
      return {
        success: false,
        message: errorMessage
      }
    }
  }



  /**
   * Save login information to storage
   * @param {Object} loginInfo
   */
  saveLoginInfo (loginInfo) {
    try {
      uni.setStorageSync(STORAGE_KEYS.LOGIN_INFO, loginInfo)

      // For backward compatibility with existing code
      uni.setStorageSync(STORAGE_KEYS.ADMIN_LOGIN_INFO, {
        username: loginInfo.username,
        userType: loginInfo.userType
      })

      // Save user permissions
      uni.setStorageSync(STORAGE_KEYS.USER_PERMISSIONS, loginInfo.permissions)

    } catch (error) {
      console.error('Error saving login info:', error)
    }
  }

  /**
   * Get current login information
   * @returns {Object|null}
   */
  getLoginInfo () {
    try {
      return uni.getStorageSync(STORAGE_KEYS.LOGIN_INFO) || null
    } catch (error) {
      console.error('Error getting login info:', error)
      return null
    }
  }

  /**
   * Check if user is logged in
   * @returns {boolean}
   */
  isLoggedIn () {
    const loginInfo = this.getLoginInfo()
    return !!(loginInfo && loginInfo.username)
  }

  /**
   * Check if current user has specific permission
   * @param {string} permission 
   * @returns {boolean}
   */
  hasPermission (permission) {
    try {
      const permissions = uni.getStorageSync(STORAGE_KEYS.USER_PERMISSIONS) || []
      return permissions.includes('*') || permissions.includes(permission)
    } catch (error) {
      console.error('Error checking permission:', error)
      return false
    }
  }

  /**
   * Get current user type
   * @returns {string|null}
   */
  getUserType () {
    const loginInfo = this.getLoginInfo()
    return loginInfo ? loginInfo.userType : null
  }

  /**
   * Get current username
   * @returns {string|null}
   */
  getUsername () {
    const loginInfo = this.getLoginInfo()
    return loginInfo ? loginInfo.username : null
  }

  /**
   * Get current user display name
   * @returns {string|null}
   */
  getUserName () {
    const loginInfo = this.getLoginInfo()
    return loginInfo ? loginInfo.name : null
  }

  /**
   * Logout current user
   */
  async logout () {
    try {
      // 调用服务端登出API
      try {
        await authApi.logout()
      } catch (apiError) {
        console.warn('Server logout failed:', apiError)
        // 即使服务端登出失败，也继续清理本地数据
      }

      // 清理本地存储
      uni.removeStorageSync(STORAGE_KEYS.LOGIN_INFO)
      uni.removeStorageSync(STORAGE_KEYS.ADMIN_LOGIN_INFO)
      uni.removeStorageSync(STORAGE_KEYS.USER_PERMISSIONS)

      // Hide tab bar
      uni.hideTabBar()

      return true
    } catch (error) {
      console.error('Error during logout:', error)
      return false
    }
  }

  /**
   * 获取当前用户信息（从服务器）
   * @returns {Promise<Object|null>} 用户信息或null
   */
  async fetchCurrentUserInfo () {
    try {
      const response = await authApi.getUserInfo()

      if (response && response.success && response.data) {
        const userInfo = response.data

        // 更新本地存储的用户信息
        const loginInfo = this.getLoginInfo()
        if (loginInfo) {
          loginInfo.userId = userInfo.userId
          loginInfo.username = userInfo.username
          loginInfo.nickName = userInfo.nickName
          loginInfo.name = userInfo.nickName || userInfo.username
          loginInfo.userType = USER_TYPES[userInfo.userType] || 'employee'
          loginInfo.userTypeCode = userInfo.userType

          // 添加更多用户信息字段
          loginInfo.avatar = userInfo.avatar || '/static/images/avatar-placeholder.png'
          loginInfo.email = userInfo.email || ''
          loginInfo.phone = userInfo.phone || userInfo.mobile || ''
          loginInfo.realName = userInfo.realName || userInfo.nickName || userInfo.username
          loginInfo.department = userInfo.department || ''
          loginInfo.position = userInfo.position || ''
          loginInfo.lastLoginTime = userInfo.lastLoginTime || ''

          this.saveLoginInfo(loginInfo)
        }

        return userInfo
      }

      return null
    } catch (error) {
      console.error('Failed to fetch user info:', error)
      return null
    }
  }

  /**
   * 获取完整的用户显示信息（结合本地存储和服务器数据）
   * @param {boolean} forceRefresh - 是否强制从服务器刷新
   * @returns {Promise<Object>} 用户显示信息
   */
  async getCompleteUserInfo (forceRefresh = false) {
    if (!this.isLoggedIn()) {
      return {
        name: '未登录',
        username: '',
        userType: '',
        userTypeText: '游客',
        avatar: '/static/images/avatar-placeholder.png',
        email: '',
        phone: '',
        realName: '',
        department: '',
        position: '',
        lastLoginTime: ''
      }
    }

    let loginInfo = this.getLoginInfo()

    // 如果强制刷新或者本地信息不完整，从服务器获取最新信息
    if (forceRefresh || !loginInfo.avatar || !loginInfo.email) {
      const serverUserInfo = await this.fetchCurrentUserInfo()
      if (serverUserInfo) {
        loginInfo = this.getLoginInfo() // 重新获取更新后的信息
      }
    }

    const typeMap = {
      'employee': '员工',
      'manager': '管理',
      'admin': '超管'
    }

    return {
      userId: loginInfo.userId || '',
      name: loginInfo.name || loginInfo.nickName || loginInfo.username || '用户',
      username: loginInfo.username || '',
      nickName: loginInfo.nickName || '',
      realName: loginInfo.realName || loginInfo.nickName || loginInfo.username || '',
      userType: loginInfo.userType || '',
      userTypeText: typeMap[loginInfo.userType] || '用户',
      userTypeCode: loginInfo.userTypeCode || '',
      avatar: loginInfo.avatar || '/static/images/avatar-placeholder.png',
      email: loginInfo.email || '',
      phone: loginInfo.phone || '',
      department: loginInfo.department || '',
      position: loginInfo.position || '',
      lastLoginTime: loginInfo.lastLoginTime || '',
      loginTime: loginInfo.loginTime || '',
      permissions: loginInfo.permissions || []
    }
  }

  /**
   * Check if login session is valid (not expired)
   * @returns {boolean}
   */
  isSessionValid () {
    const loginInfo = this.getLoginInfo()
    if (!loginInfo || !loginInfo.loginTime) {
      return false
    }

    // Session expires after 7 days
    const sessionDuration = 7 * 24 * 60 * 60 * 1000 // 7 days in milliseconds
    const currentTime = new Date().getTime()

    return (currentTime - loginInfo.loginTime) < sessionDuration
  }

  /**
   * Refresh login session
   */
  refreshSession () {
    const loginInfo = this.getLoginInfo()
    if (loginInfo) {
      loginInfo.loginTime = new Date().getTime()
      this.saveLoginInfo(loginInfo)
    }
  }

  /**
   * Redirect to login page
   */
  redirectToLogin () {
    uni.reLaunch({
      url: '/pages/login/index'
    })
  }

  /**
   * Redirect to main page based on user type
   */
  redirectToMain () {
    const userType = this.getUserType()

    uni.showToast({
      title: '登录成功',
      icon: 'success',
      duration: 1500
    })

    console.log(`登录成功! 用户类型: ${userType}`)
    console.log('正在跳转到仪表板: /pages/index/index')

    // Use setTimeout to allow toast to show before redirect
    setTimeout(() => {
      // 所有用户都跳转到仪表板页面（TabBar页面）
      // 使用 reLaunch 确保清除登录页面历史，并且支持跳转到TabBar页面
      uni.reLaunch({
        url: '/pages/index/index',
        success: () => {
          console.log('跳转成功')
          // 显示TabBar（如果当前页面支持）
          try {
            uni.showTabBar({
              success: () => {
                console.log('TabBar显示成功')
              },
              fail: (err) => {
                console.log('TabBar显示失败:', err)
              }
            })
          } catch (e) {
            console.log('TabBar操作异常:', e)
          }
        },
        fail: (err) => {
          console.error('页面跳转失败:', err)
          // 如果 reLaunch 失败，尝试使用 switchTab
          uni.switchTab({
            url: '/pages/index/index',
            success: () => {
              console.log('switchTab跳转成功')
            },
            fail: (err2) => {
              console.error('switchTab也失败了:', err2)
              // 最后的备选方案：使用 navigateTo
              uni.navigateTo({
                url: '/pages/index/index'
              })
            }
          })
        }
      })
    }, 1500)
  }

  /**
   * Utility function to create delay
   * @param {number} ms 
   * @returns {Promise}
   */
  delay (ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// Create singleton instance
const authService = new AuthService()

// Export the service instance and utilities
export default authService
export { AuthService, STORAGE_KEYS }

// Export commonly used functions for convenience
export const getLoginInfo = () => authService.getLoginInfo()
export const isLoggedIn = () => authService.isLoggedIn()
export const getUserType = () => authService.getUserType()
export const getUsername = () => authService.getUsername()
export const getUserName = () => authService.getUserName()
