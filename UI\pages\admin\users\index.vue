<template>
  <view class="container user-management-container">
    <!-- 顶部控制区域 - 美化红框部分 -->
    <view class="control-section">
      <!-- 切换选项卡 -->
      <view class="tab-header">
        <view class="tab-item" :class="{ active: activeTab === 'managers' }" @tap="switchTab('managers')">管理
        </view>

        <view class="tab-item" :class="{ active: activeTab === 'employees' }" @tap="switchTab('employees')">员工
        </view>
        <view class="tab-item" :class="{ active: activeTab === 'users' }" @tap="switchTab('users')">用户</view>
      </view>

      <!-- 时间筛选 -->
      <TimeFilter v-model="activeTimeFilter" @change="handleTimeFilterChange" />

      <!-- 搜索栏和添加按钮 -->
      <view class="search-box">
        <view class="search-input">
          <image src="/assets/images/search.png" mode="widthFix" class="search-icon"></image>
          <input type="text" v-model="searchKeyword" placeholder="搜索名称或手机号" @input="handleSearch" />
        </view>
        <view class="add-btn-container">
          <view class="add-btn" @tap="generateInviteLink()">
            <text class="iconfont icon-add"></text>
            <text>添加{{ getAddButtonText() }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 用户列表区域 - 固定高度并添加滚动条 -->
    <view class="list-container" :style="{
      height: listHeight ? listHeight + 'px' : 'calc(100vh - 380rpx)',
    }">
      <!-- 用户列表 -->
      <scroll-view class="scroll-list" scroll-y v-if="activeTab === 'users'">
        <view class="user-card" v-for="user in filteredUsers" :key="user.id"
          @tap="viewUserDetail(formattedUserInfo(user))">
          <UserInfoCard :userInfo="formattedUserInfo(user)" :showDetailBtn="false" :showFooterBtns="false" />
        </view>
        <view class="list-bottom-space"></view>
      </scroll-view>

      <!-- 员工列表 -->
      <scroll-view class="scroll-list" scroll-y v-if="activeTab === 'employees'">
        <view class="user-card" v-for="employee in filteredEmployees" :key="employee.id"
          @tap="viewEmployeeDetail(formattedEmployeeInfo(employee))">
          <UserInfoCard :userInfo="formattedEmployeeInfo(employee)" :timeFilter="activeTimeFilter"
            :customDateRange="customDateRange" :showDetailBtn="false" :showFooterBtns="false" />
        </view>
        <view class="list-bottom-space"></view>
      </scroll-view>

      <!-- 管理列表 -->
      <scroll-view class="scroll-list" scroll-y v-if="activeTab === 'managers'">
        <view class="user-card" v-for="manager in filteredManagers" :key="manager.id"
          @tap="viewManagerDetail(formattedManagerInfo(manager))">
          <UserInfoCard :userInfo="formattedManagerInfo(manager)" :timeFilter="activeTimeFilter"
            :customDateRange="customDateRange" :showDetailBtn="false" :showFooterBtns="false" />
        </view>
        <view class="list-bottom-space"></view>
      </scroll-view>
    </view>

    <!-- 邀请链接弹窗 -->
    <view class="invite-modal" v-if="showInviteModal">
      <view class="modal-content">
        <view class="modal-header">
          <text class="modal-title">邀请链接</text>
          <text class="close-btn" @tap="closeInviteModal">×</text>
        </view>
        <view class="modal-body">
          <text class="invite-text">复制以下链接分享给{{ inviteRoleText }}：</text>
          <view class="invite-link">{{ inviteLink }}</view>
          <button class="copy-btn" @tap="copyInviteLink">复制链接</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>

import UserInfoCard from "../../../components/UserInfoCard.vue";
import TimeFilter from "../../../components/TimeFilter.vue";
import {
  getSysUserEmployees,
  getSysUserSubordinatesById
} from "../../../api/sysuser.js";
import {
  queryUsers
} from "../../../api/user.js";
import { formatSysUserData, formatManagerData, formatEmployeeData, formatUserData } from "../../../utils/employee-data-mapper.js";
import { apiCallWrapper, ErrorHandlerPresets } from "../../../utils/api-error-handler.js";

export default {
  components: {
    UserInfoCard,
    TimeFilter,
  },
  data () {
    return {
      users: [],
      employees: [],
      managers: [],
      searchKeyword: "",
      activeTab: "managers", // 'users', 'employees', or 'managers'
      activeTimeFilter: "today", // 'today', 'yesterday', 'thisWeek', 'thisMonth', 'custom'
      customDateRange: {
        startDate: "",
        endDate: "",
      },
      selectedEmployeeId: null,
      selectedManagerId: null,
      currentUserRole: "admin", // 'admin', 'agent', 'employee'
      showInviteModal: false,
      inviteLink: "",
      inviteRoleText: "",
      statusBarHeight: 0,
      windowHeight: 0,
      listHeight: 0,
      safeAreaBottom: 0,
    };
  },
  computed: {
    filteredUsers () {
      let userList = this.users;

      // 如果选择了员工，只显示该员工的用户
      if (this.selectedEmployeeId) {
        userList = userList.filter(
          (user) => user.employeeId === this.selectedEmployeeId
        );
      }
      // 如果选择了管理，只显示该管理下员工的用户
      else if (this.selectedManagerId) {
        userList = userList.filter((user) => {
          const employee = this.employees.find((e) => e.id === user.employeeId);
          return employee && employee.managerId === this.selectedManagerId;
        });
      }

      // 搜索过滤
      if (!this.searchKeyword) {
        return userList;
      }

      const keyword = this.searchKeyword.toLowerCase();
      return userList.filter(
        (user) =>
          user.username.toLowerCase().includes(keyword) ||
          (user.phone && user.phone.includes(keyword))
      );
    },
    filteredEmployees () {
      let employeeList = this.employees;

      // 如果选择了管理，只显示该管理的员工
      if (this.selectedManagerId) {
        employeeList = employeeList.filter(
          (employee) => employee.managerId === this.selectedManagerId
        );
      }

      // 搜索过滤
      if (!this.searchKeyword) {
        return employeeList;
      }

      const keyword = this.searchKeyword.toLowerCase();
      return employeeList.filter(
        (employee) =>
          employee.username.toLowerCase().includes(keyword) ||
          (employee.phone && employee.phone.includes(keyword))
      );
    },
    filteredManagers () {
      if (!this.searchKeyword) {
        return this.managers;
      }

      const keyword = this.searchKeyword.toLowerCase();
      return this.managers.filter(
        (manager) =>
          manager.username.toLowerCase().includes(keyword) ||
          (manager.phone && manager.phone.includes(keyword))
      );
    },
  },
  async onLoad () {
    // 模拟当前用户角色
    this.currentUserRole = "admin"; // 可以是 'admin', 'agent', 'employee'

    // 根据用户角色加载数据
    await this.loadDataByRole();

    // 获取状态栏高度和安全区域
    this.getSystemInfo();

    // 修复页面布局问题
    this.fixPageLayout();

    // 计算列表高度
    this.calculateListHeight();
  },
  onShow () {
    // 修复tabbar层级问题
    this.fixTabBarZIndex();

    // 修复页面布局问题
    this.fixPageLayout();

    // 计算列表高度
    this.calculateListHeight();
  },
  onReady () {
    // 计算列表高度
    this.calculateListHeight();
  },
  // 监听屏幕旋转或窗口大小变化
  onResize () {
    this.calculateListHeight();
  },
  methods: {
    // 根据用户角色加载数据
    async loadDataByRole () {
      try {
        uni.showLoading({
          title: '加载中...'
        });

        // 根据当前用户角色决定加载哪些数据
        switch (this.currentUserRole) {
          case 'admin':
          case 'super_admin':
            // 超管和管理员可以查看所有数据
            await Promise.all([
              this.loadManagers(),
              this.loadEmployees(),
              this.loadUsers()
            ]);
            break;

          case 'manager':
          case 'agent':
            // 管理只能查看自己的下级员工和用户
            const currentUserId = 1; // 假设当前登录用户ID，实际应从认证信息获取
            await Promise.all([
              this.loadSubordinateEmployees(currentUserId),
              this.loadSubordinateUsers(currentUserId)
            ]);
            // 管理不能查看其他管理
            this.managers = [];
            break;

          case 'employee':
            // 员工只能查看自己管理的用户
            const currentEmployeeId = 101; // 假设当前登录员工ID
            await this.loadSubordinateUsers(currentEmployeeId);
            // 员工不能查看管理和其他员工
            this.managers = [];
            this.employees = [];
            break;

          default:
            // 默认情况显示空数据
            this.managers = [];
            this.employees = [];
            this.users = [];
        }

        uni.hideLoading();
      } catch (error) {
        console.error('加载数据失败:', error);
        uni.hideLoading();

        // API失败时显示空数据
        this.managers = [];
        this.employees = [];
        this.users = [];

        uni.showToast({
          title: '加载失败',
          icon: 'none'
        });
      }
    },

    // 加载管理列表
    async loadManagers () {
      try {
        const response = await queryEmployees({
          pageIndex: 1,
          pageSize: 100,
          level: 2 // 管理级别
        });

        if (response.success && response.data) {
          this.managers = response.data.items.map(manager => formatManagerData(manager));
        }
      } catch (error) {
        console.error('加载管理列表失败:', error);
        this.managers = [];
      }
    },

    // 加载员工列表
    async loadEmployees () {
      try {
        const response = await getSysUserEmployees({
          pageIndex: 1,
          pageSize: 100
        });

        if (response.success && response.data) {
          this.employees = response.data.map(employee => formatSysUserData(employee));
        }
      } catch (error) {
        console.error('加载员工列表失败:', error);
        this.employees = [];
      }
    },

    // 加载用户列表
    async loadUsers () {
      try {
        const response = await queryUsers({
          pageIndex: 1,
          pageSize: 100
        });

        if (response.success && response.data) {
          this.users = response.data.items.map(user => formatUserData(user));
        }
      } catch (error) {
        console.error('加载用户列表失败:', error);
        this.users = [];
      }
    },

    // 加载下级员工
    async loadSubordinateEmployees (managerId) {
      try {
        const response = await getSysUserSubordinatesById(managerId, {
          pageIndex: 1,
          pageSize: 100
        });

        if (response.success && response.data) {
          this.employees = response.data.map(employee => formatSysUserData(employee));
        }
      } catch (error) {
        console.error('加载下级员工失败:', error);
        this.employees = [];
      }
    },

    // 加载下级用户
    async loadSubordinateUsers (employeeId) {
      try {
        const response = await queryUsers({
          employeeId: employeeId,
          pageIndex: 1,
          pageSize: 100
        });

        if (response.success && response.data) {
          this.users = response.data.items.map(user => formatUserData(user));
        }
      } catch (error) {
        console.error('加载下级用户失败:', error);
        this.users = [];
      }
    },





    // 处理时间筛选变化
    handleTimeFilterChange (timeRange) {
      this.activeTimeFilter = timeRange;
      this.customDateRange = {
        startDate: timeRange.startDate,
        endDate: timeRange.endDate
      };
      console.log("时间筛选变化:", timeRange);
      // 这里可以根据需要执行其他操作，如重新加载数据等
    },

    // 获取系统信息
    getSystemInfo () {
      try {
        const systemInfo = uni.getSystemInfoSync();
        this.statusBarHeight = systemInfo.statusBarHeight || 0;
        this.windowHeight = systemInfo.windowHeight || 0;
        this.safeAreaBottom = systemInfo.safeAreaInsets
          ? systemInfo.safeAreaInsets.bottom || 0
          : 0;
      } catch (e) {
        console.error("获取系统信息失败", e);
      }
    },

    // 计算列表高度
    calculateListHeight () {
      const that = this;
      // 获取系统信息
      uni.getSystemInfo({
        success (res) {
          that.windowHeight = res.windowHeight;
          // 延迟执行，确保DOM已渲染
          setTimeout(() => {
            // 获取控制区域高度
            const query = uni.createSelectorQuery().in(that);
            query.select(".control-section").boundingClientRect();
            query.select(".page-header").boundingClientRect();
            query.exec((data) => {
              if (data && data[0] && data[1]) {
                const controlHeight = data[0].height;
                const headerHeight = data[1].height;
                // 计算列表区域高度 = 窗口高度 - 控制区域高度 - 页面标题高度 - tabbar高度(如果有)
                const tabbarHeight = 60; // 估计的tabbar高度
                const bottomSafeArea = that.safeAreaBottom || 0;
                that.listHeight =
                  that.windowHeight -
                  controlHeight -
                  headerHeight -
                  tabbarHeight -
                  bottomSafeArea;
                // 更新样式
                that.updateListHeight();
              }
            });
          }, 300);
        },
      });
    },

    // 更新列表高度
    updateListHeight () {
      // if (typeof document !== "undefined") {
      //   const listContainer = document.querySelector(".list-container");
      //   if (listContainer && this.listHeight > 0) {
      //     listContainer.style.height = `${this.listHeight}px`;
      //   }

      //   // 确保滚动区域的高度正确
      //   const scrollLists = document.querySelectorAll(".scroll-list");
      //   scrollLists.forEach((list) => {
      //     if (list) {
      //       list.style.height = "100%";
      //     }
      //   });
      // }
    },

    // 修复页面布局问题
    fixPageLayout () {
      // 在H5环境中
      if (typeof document !== "undefined") {
        setTimeout(() => {
          // 移除可能导致空白区域的元素或样式
          const pageHeadElements = document.querySelectorAll(
            ".uni-page-head, .uni-page-head-bd"
          );
          pageHeadElements.forEach((element) => {
            if (element) {
              element.style.display = "none";
            }
          });

          // 调整页面容器样式
          const containerElements = document.querySelectorAll(
            ".container, .user-management-container"
          );
          containerElements.forEach((element) => {
            if (element) {
              element.style.paddingTop = "0";
              element.style.marginTop = "0";
              element.style.overflow = "hidden";
              element.style.height = "100vh";
            }
          });

          // 确保滚动区域的样式正确
          const scrollLists = document.querySelectorAll(".scroll-list");
          scrollLists.forEach((list) => {
            if (list) {
              list.style.height = "100%";
              list.style.overflowY = "auto";
              list.style.WebkitOverflowScrolling = "touch";
            }
          });
        }, 100);
      }
    },

    // 获取状态栏高度
    getStatusBarHeight () {
      // 在小程序环境中
      if (typeof uni !== "undefined" && uni.getSystemInfoSync) {
        try {
          const systemInfo = uni.getSystemInfoSync();
          this.statusBarHeight = systemInfo.statusBarHeight || 0;
        } catch (e) {
          this.statusBarHeight = 0;
        }
      }
    },

    // 返回上一页
    goBack () {
      uni.navigateBack({
        delta: 1,
      });
    },

    // 修复tabbar层级问题
    fixTabBarZIndex () {
      // 在小程序环境中
      if (typeof uni !== "undefined") {
        setTimeout(() => {
          uni.setTabBarStyle({
            zIndex: 9999,
          });
        }, 100);
      }
      // 在H5环境中
      else if (typeof document !== "undefined") {
        setTimeout(() => {
          const tabbarElements = document.querySelectorAll(
            ".uni-tabbar, .uni-tabbar-bottom, .uni-tabbar__content, uni-tabbar, .tab-bar, .tabbar"
          );
          tabbarElements.forEach((element) => {
            if (element) {
              element.style.zIndex = "9999";
            }
          });
        }, 100);
      }
    },
    // 格式化用户信息，添加类型标识
    formattedUserInfo (user) {
      return {
        ...user,
        type: "user",
      };
    },

    // 格式化员工信息，添加类型标识
    formattedEmployeeInfo (employee) {
      return {
        ...employee,
        type: "employee",
      };
    },

    // 格式化管理信息，添加类型标识
    formattedManagerInfo (manager) {
      return {
        ...manager,
        type: "manager",
      };
    },

    async switchTab (tab) {
      this.activeTab = tab;
      this.searchKeyword = "";
      this.selectedEmployeeId = null;
      this.selectedManagerId = null;

      // 切换标签时重新加载对应的数据
      try {
        switch (tab) {
          case 'managers':
            if (this.managers.length === 0) {
              await this.loadManagers();
            }
            break;
          case 'employees':
            if (this.employees.length === 0) {
              if (this.currentUserRole === 'admin' || this.currentUserRole === 'super_admin') {
                await this.loadEmployees();
              } else {
                const currentUserId = 1; // 实际应从认证信息获取
                await this.loadSubordinateEmployees(currentUserId);
              }
            }
            break;
          case 'users':
            if (this.users.length === 0) {
              if (this.currentUserRole === 'admin' || this.currentUserRole === 'super_admin') {
                await this.loadUsers();
              } else {
                const currentUserId = this.currentUserRole === 'manager' ? 1 : 101;
                await this.loadSubordinateUsers(currentUserId);
              }
            }
            break;
        }
      } catch (error) {
        console.error('切换标签加载数据失败:', error);
      }
    },

    // 获取添加按钮文本
    getAddButtonText () {
      switch (this.activeTab) {
        case "users":
          return "用户";
        case "employees":
          return "员工";
        case "managers":
          return "管理";
        default:
          return "";
      }
    },

    // 检查当前用户是否可以添加当前选中的角色
    canAddCurrentRole () {
      if (this.currentUserRole === "admin") {
        return this.activeTab === "managers"; // 管理员只能添加管理
      } else if (this.currentUserRole === "agent") {
        return this.activeTab === "employees"; // 管理只能添加员工
      } else if (this.currentUserRole === "employee") {
        return this.activeTab === "users"; // 员工只能添加用户
      }
      return false;
    },

    // 生成邀请链接
    generateInviteLink () {
      // 生成不同的邀请链接
      let baseUrl = "https://example.com/register?";
      let params = "";

      if (this.currentUserRole === "admin" && this.activeTab === "managers") {
        // 管理员生成管理邀请链接
        params = `role=manager&inviter=admin`;
        this.inviteRoleText = "管理";
      } else if (
        this.currentUserRole === "agent" &&
        this.activeTab === "employees"
      ) {
        // 管理生成员工邀请链接
        const managerId = 1; // 假设当前登录的管理ID为1
        params = `role=employee&inviter=manager&managerId=${managerId}`;
        this.inviteRoleText = "员工";
      } else if (
        this.currentUserRole === "employee" &&
        this.activeTab === "users"
      ) {
        // 员工生成用户邀请链接
        const employeeId = 101; // 假设当前登录的员工ID为101
        params = `role=user&inviter=employee&employeeId=${employeeId}`;
        this.inviteRoleText = "用户";
      }

      this.inviteLink = baseUrl + params;
      this.showInviteModal = true;
    },

    // 复制邀请链接
    copyInviteLink () {
      uni.setClipboardData({
        data: this.inviteLink,
        success: () => {
          uni.showToast({
            title: "链接已复制",
            icon: "success",
          });
        },
      });
    },

    // 关闭邀请弹窗
    closeInviteModal () {
      this.showInviteModal = false;
    },

    // 根据时间筛选获取统计数据
    getStatByTime (item, statType) {
      let timeKey = "";
      switch (this.activeTimeFilter) {
        case "today":
          timeKey = "today";
          break;
        case "yesterday":
          timeKey = "yesterday";
          break;
        case "thisWeek":
          timeKey = "thisWeek";
          break;
        case "thisMonth":
          timeKey = "thisMonth";
          break;
        default:
          timeKey = "today";
      }

      let statKey = "";
      switch (statType) {
        case "views":
          statKey = "totalViews";
          break;
        case "quizzes":
          statKey = "totalQuizzes";
          break;
        case "rewards":
          statKey = "totalRewards";
          break;
        default:
          return 0;
      }

      return item[statKey] && item[statKey][timeKey]
        ? item[statKey][timeKey]
        : 0;
    },

    viewUserDetail (user) {
      uni.navigateTo({
        url: `/pages/admin/users/info?userId=${user.id}`,
      });
    },

    viewEmployeeDetail (employee) {
      // 导航到员工详情页面
      uni.navigateTo({
        url: `/pages/admin/users/member-list?id=${employee.id}&type=employee`,
      });
    },

    viewManagerDetail (manager) {
      // 导航到管理详情页面
      uni.navigateTo({
        url: `/pages/admin/users/member-list?id=${manager.id}&type=manager`,
      });
    },

    viewEmployeeUsers (employee) {
      this.activeTab = "users";
      this.selectedEmployeeId = employee.id;

      // 获取员工名称
      const employeeObj = this.employees.find((e) => e.id === employee.id);
      const employeeName = employeeObj ? employeeObj.username : "";

      uni.showToast({
        title: `查看${employeeName}的用户`,
        icon: "none",
      });
    },

    viewManagerEmployees (manager) {
      this.activeTab = "employees";
      this.selectedManagerId = manager.id;

      // 获取管理名称
      const managerObj = this.managers.find((m) => m.id === manager.id);
      const managerName = managerObj ? managerObj.username : "";

      uni.showToast({
        title: `查看${managerName}的员工`,
        icon: "none",
      });
    },

    handleSearch () {
      // Search is already handled by computed properties
      // This method is just to handle the input event
    },
  },
};
</script>

<style lang="scss">
@import '@/styles/index.scss';

/* 页面特定样式 */
.page-header {
  @extend .page-header-custom;
}

.page-title {
  @extend .page-title;
}

/* 添加按钮 */
.add-btn-container {
  display: flex;
  align-items: center;
}

.add-btn {
  @extend .btn;
  @extend .btn-primary;
  border-radius: $border-radius-xl;
  white-space: nowrap;
  box-shadow: $shadow-base;
}

.add-btn .iconfont {
  margin-right: $spacing-xs;
}

/* 列表容器 */
.list-container {
  @extend .scrollable-content;
  margin-top: 330rpx;
  padding: 0 $page-padding;
}

.scroll-list {
  height: 100%;
  padding: 0 0 120rpx 0;
  box-sizing: border-box;
}

.user-card {
  @extend .user-card;
}

/* 
.user-card::after {
	content: '';
	position: absolute;
	top: 50%;
	right: 30rpx;
	transform: translateY(-50%);
	width: 16rpx;
	height: 16rpx;
	border-top: 4rpx solid #ccc;
	border-right: 4rpx solid #ccc;
	transform: translateY(-50%) rotate(45deg);
} */

/* 列表底部空间 */
.list-bottom-space {
  height: 120rpx;
}

/* 邀请弹窗样式 */
.invite-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.modal-content {
  width: 80%;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.15);
}

.modal-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
}

.close-btn {
  font-size: 40rpx;
  color: #999;
}

.modal-body {
  padding: 30rpx;
}

.invite-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.invite-link {
  background-color: #f5f5f5;
  padding: 20rpx;
  border-radius: 8rpx;
  word-break: break-all;
  margin-bottom: 20rpx;
  font-size: 26rpx;
  color: #333;
  border: 1rpx dashed #ddd;
}

.copy-btn {
  background-color: #186BFF;
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;
  margin-top: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
}

.search-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}
</style>