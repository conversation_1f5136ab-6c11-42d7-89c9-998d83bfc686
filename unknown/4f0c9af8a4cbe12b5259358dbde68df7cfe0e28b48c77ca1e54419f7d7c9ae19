<template>
  <view class="user-info-card">
    <!-- 用户头像和基本信息 -->
    <view class="user-header">
      <view class="user-avatar" :style="{ backgroundImage: `url(${userInfo.avatar})` }"></view>
      <view class="user-basic-info">
        <view class="user-top-row">
          <view class="user-name-container">
            <text class="user-name">{{
              userInfo.username || userInfo.nickname
            }}</text>
            <view class="copy-id-btn" @tap.stop="copyUserId">
              <text class="copy-id-icon">复制ID</text>
            </view>
          </view>
          <view class="user-type">
            <text class="type-tag" :class="getRoleTagClass()">
              {{ getRoleText() }}
            </text>
          </view>
        </view>
        <view class="user-phone">{{ userInfo.phone }}</view>

        <!-- 注册和关系信息 -->
        <view class="user-meta-info">
          <view class="meta-item">
            <text class="meta-label">注册:</text>
            <text class="meta-value">{{
              formatDate(userInfo.registerTime)
            }}</text>
          </view>

          <view class="meta-item" v-if="userInfo.type === 'employee'">
            <text class="meta-label">推广:</text>
            <text class="meta-value">{{ userInfo.userCount || 0 }}人</text>
          </view>

          <view class="meta-item" v-if="userInfo.type === 'agent'">
            <text class="meta-label">管理:</text>
            <text class="meta-value">{{ userInfo.employeeCount || userInfo.userCount || 0 }}人</text>
          </view>

          <view class="meta-item" v-if="userInfo.type === 'user' && userInfo.employeeId">
            <text class="meta-label">所属:</text>
            <text class="meta-value link-text" @tap="viewEmployee(userInfo.employeeId)">{{ userInfo.employeeName || "员工"
            }}</text>
          </view>

          <view class="meta-item" v-if="userInfo.type === 'employee' && userInfo.managerId">
            <text class="meta-label">所属:</text>
            <text class="meta-value link-text" @tap="viewManager(userInfo.managerId)">{{
              userInfo.managerName || "管理"
            }}</text>
          </view>
        </view>
      </view>
      <view class="action-btn" v-if="showActionBtn" @tap="handleAction">
        <text class="iconfont" :class="actionIcon"></text>
      </view>
    </view>

    <!-- 统计数据 -->
    <view class="stats-section">
      <view class="stat-item">
        <view class="stat-content">
          <text class="stat-value">观看视频:{{
            userInfo.watchedVideos ||
            (userInfo.totalViews && userInfo.totalViews[actualTimeFilter]) ||
            0
          }}</text>
        </view>
      </view>
      <view class="stat-item">
        <view class="stat-content">
          <text class="stat-value">答题数量:{{
            userInfo.completedQuizzes ||
            (userInfo.totalQuizzes &&
              userInfo.totalQuizzes[actualTimeFilter]) ||
            0
          }}</text>
        </view>
      </view>

      <view class="stat-item">
        <view class="stat-content">
          <text class="stat-value">{{ userInfo.type !== "user" ? "奖励总额" : "获得奖励" }}:{{
            (userInfo.totalRewards &&
              (typeof userInfo.totalRewards === "object"
                ? userInfo.totalRewards[actualTimeFilter]
                : userInfo.totalRewards)) ||
            0
          }}</text>
        </view>
      </view>
    </view>

    <!-- 额外信息(插槽) -->
    <view class="extra-section" v-if="$slots.extra">
      <slot name="extra"></slot>
    </view>

    <!-- 底部操作按钮 -->
    <view class="footer-btns" v-if="showFooterBtns">
      <view class="footer-btn detail-btn" v-if="showDetailBtn" @tap="viewDetail">
        <text class="iconfont icon-detail"></text>
        <text>查看详情</text>
      </view>
      <view class="footer-btn users-btn" v-if="userInfo.type === 'employee' && showUsersBtn" @tap="viewUsers">
        <text class="iconfont icon-users"></text>
        <text>查看用户</text>
      </view>
      <view class="footer-btn employees-btn" v-if="userInfo.type === 'agent' && showEmployeesBtn" @tap="viewEmployees">
        <text class="iconfont icon-users"></text>
        <text>查看员工</text>
      </view>
      <view class="footer-btn account-btn" v-if="showAccountBtn" @tap.stop="handleAccountAction">
        <text class="iconfont icon-account"></text>
        <text>{{ userInfo.disabled ? "启用账号" : "禁用账号" }}</text>
      </view>
      <slot name="buttons"></slot>
    </view>


  </view>
</template>

<script>
export default {
  name: "UserInfoCard",
  props: {
    userInfo: {
      type: Object,
      required: true,
    },
    showActionBtn: {
      type: Boolean,
      default: false,
    },
    actionIcon: {
      type: String,
      default: "icon-more",
    },
    showFooterBtns: {
      type: Boolean,
      default: true,
    },
    showDetailBtn: {
      type: Boolean,
      default: true,
    },
    showUsersBtn: {
      type: Boolean,
      default: true,
    },
    showEmployeesBtn: {
      type: Boolean,
      default: true,
    },
    showAccountBtn: {
      type: Boolean,
      default: true,
    },
    timeFilter: {
      type: [String, Object],
      default: "today",
    },
    customDateRange: {
      type: Object,
      default: null,
    },
  },
  data () {
    return {
      isCopying: false, // 添加复制状态标志，防止重复触发
    };
  },
  computed: {
    // 获取实际的时间筛选值
    actualTimeFilter () {
      // 如果 timeFilter 是对象（新格式），返回其 type 属性
      if (typeof this.timeFilter === 'object' && this.timeFilter.type) {
        return this.timeFilter.type;
      }
      // 如果是字符串（旧格式），直接返回
      if (typeof this.timeFilter === 'string') {
        return this.timeFilter;
      }
      // 默认值
      return "today";
    },
  },
  methods: {
    getRoleText () {
      switch (this.userInfo.type) {
        case "agent":
          return "代理";
        case "employee":
          return "员工";
        case "user":
          return "用户";
        default:
          return "未知";
      }
    },
    getRoleTagClass () {
      switch (this.userInfo.type) {
        case "agent":
          return "agent-tag";
        case "employee":
          return "employee-tag";
        case "user":
          return "user-tag";
        default:
          return "";
      }
    },
    formatDate (dateString) {
      if (!dateString) return "";
      // 只保留日期部分，不显示时间
      if (dateString.includes(" ")) {
        return dateString.split(" ")[0];
      }
      return dateString;
    },
    copyUserId () {
      if (!this.userInfo || !this.userInfo.id) return;

      // 防止重复触发
      if (this.isCopying) return;

      this.isCopying = true;

      // 复制ID到剪贴板，使用系统默认提示
      uni.setClipboardData({
        data: String(this.userInfo.id),
        success: () => {
          // 复制成功，重置状态
          this.isCopying = false;
        },
        fail: () => {
          // 复制失败时重置状态
          this.isCopying = false;
        }
      });

      // 阻止事件冒泡
      return false;
    },
    handleAction () {
      this.$emit("action", this.userInfo);
    },
    viewDetail () {
      this.$emit("view-detail", this.userInfo);
    },
    viewUsers () {
      this.$emit("view-users", this.userInfo);
    },
    viewEmployees () {
      this.$emit("view-employees", this.userInfo);
    },
    viewEmployee (employeeId) {
      this.$emit("view-employee", employeeId);
    },
    viewManager (managerId) {
      this.$emit("view-manager", managerId);
    },
    handleAccountAction () {
      console.log("账号操作:", this.userInfo.username, this.userInfo.disabled);
      if (this.userInfo.disabled) {
        this.$emit("enableAccount", this.userInfo);
      } else {
        this.$emit("disableAccount", this.userInfo);
      }
    },
  },
};
</script>

<style>
.user-info-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%);
  border: 1rpx solid #e8f4ff;
  border-radius: 20rpx;
  padding: 14rpx;
  box-shadow: 0 6rpx 16rpx rgba(24, 107, 255, 0.08);
  position: relative;
  overflow: hidden;
}

/* 
.user-info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6rpx;
  height: 100%;
  background: linear-gradient(180deg, #186BFF, #4A90FF);
  border-radius: 0 3rpx 3rpx 0;
} */

/* 用户头像和基本信息 */
.user-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.user-avatar {
  width: 88rpx;
  height: 88rpx;
  border-radius: 44rpx;
  background-size: cover;
  background-position: center;
  margin-right: 24rpx;
  flex-shrink: 0;
  border: 3rpx solid #e8f4ff;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.15);
  position: relative;
}

.user-avatar::after {
  content: '';
  position: absolute;
  top: -3rpx;
  left: -3rpx;
  right: -3rpx;
  bottom: -3rpx;
  border-radius: 50%;
  background: linear-gradient(45deg, #186BFF, #4A90FF);
  z-index: -1;
}

.user-basic-info {
  flex: 1;
}

.user-top-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4rpx;
}

.user-name-container {
  display: flex;
  align-items: center;
}

.user-name {
  font-size: 32rpx;
  font-weight: 700;
  color: #1a1a1a;
  text-shadow: 0 1rpx 2rpx rgba(24, 144, 255, 0.05);
}

.copy-id-btn {
  margin-left: 12rpx;
  padding: 4rpx 12rpx;
  background: linear-gradient(135deg, #e8f4ff, #f0f8ff);
  border: 1rpx solid #bae7ff;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

.copy-id-btn:active {
  background: linear-gradient(135deg, #bae7ff, #e8f4ff);
  transform: scale(0.95);
}

.copy-id-icon {
  font-size: 20rpx;
  color: #186BFF;
  font-weight: 500;
}

.user-phone {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 4rpx;
}

.user-meta-info {
  display: flex;
  flex-wrap: wrap;
  margin-top: 4rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  margin-right: 12rpx;
  font-size: 20rpx;
}

.meta-label {
  color: #999;
  margin-right: 4rpx;
}

.meta-value {
  color: #666;
}

.user-type {
  margin-left: 10rpx;
}

.type-tag {
  display: inline-block;
  font-size: 18rpx;
  padding: 2rpx 8rpx;
  border-radius: 16rpx;
}

.agent-tag {
  background-color: #fff7e6;
  color: #fa8c16;
}

.employee-tag {
  background-color: #e6f7ff;
  color: #186BFF;
}

.user-tag {
  background-color: #f6ffed;
  color: #52c41a;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.action-btn .iconfont {
  font-size: 40rpx;
  color: #999;
}

/* 信息部分 */
.info-section {
  display: none;
  /* 隐藏旧的信息部分 */
}

.link-text {
  color: #186BFF;
  font-weight: 500;
  text-decoration: underline;
  text-decoration-color: rgba(24, 107, 255, 0.3);
}

/* 统计部分 */
.stats-section {
  display: flex;
  justify-content: space-between;
  padding: 24rpx 0 0rpx 0;
  border-top: 1rpx solid #e8f4ff;
  background: linear-gradient(135deg, rgba(232, 244, 255, 0.3), rgba(240, 248, 255, 0.5));
  margin: 16rpx -32rpx 0 -32rpx;
  padding: 20rpx 32rpx 16rpx 32rpx;
  border-radius: 0 0 20rpx 20rpx;
}

.stat-item {
  display: flex;
  align-items: center;
  flex: 1;
  text-align: center;
  position: relative;
}

.stat-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1rpx;
  height: 32rpx;
  background: linear-gradient(180deg, transparent, #bae7ff, transparent);
}

.stat-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.stat-value {
  font-size: 24rpx;
  font-weight: 600;
  color: #186BFF;
  text-shadow: 0 1rpx 2rpx rgba(24, 107, 255, 0.1);
}

/* 额外信息 */
.extra-section {
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 15rpx;
}

/* 底部按钮 */
.footer-btns {
  display: flex;
  justify-content: flex-end;
  margin-top: 4rpx;
}

.footer-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6rpx 12rpx;
  border-radius: 24rpx;
  margin-left: 8rpx;
  font-size: 20rpx;
}

.footer-btn .iconfont {
  font-size: 20rpx;
  margin-right: 3rpx;
}

.detail-btn {
  background: linear-gradient(135deg, #e8f4ff, #f0f8ff);
  border: 1rpx solid #bae7ff;
  color: #186BFF;
  box-shadow: 0 2rpx 6rpx rgba(24, 107, 255, 0.1);
}

.detail-btn:active {
  background: linear-gradient(135deg, #bae7ff, #e8f4ff);
  transform: scale(0.95);
}

.users-btn {
  background: linear-gradient(135deg, #f6ffed, #f9fff6);
  border: 1rpx solid #b7eb8f;
  color: #52c41a;
  box-shadow: 0 2rpx 6rpx rgba(82, 196, 26, 0.1);
}

.users-btn:active {
  background: linear-gradient(135deg, #b7eb8f, #f6ffed);
  transform: scale(0.95);
}

.employees-btn {
  background: linear-gradient(135deg, #fff7e6, #fffaf0);
  border: 1rpx solid #ffd591;
  color: #fa8c16;
  box-shadow: 0 2rpx 6rpx rgba(250, 140, 22, 0.1);
}

.employees-btn:active {
  background: linear-gradient(135deg, #ffd591, #fff7e6);
  transform: scale(0.95);
}

.account-btn {
  background: linear-gradient(135deg, #e8f4ff, #f0f8ff);
  border: 1rpx solid #bae7ff;
  color: #186BFF;
  box-shadow: 0 2rpx 6rpx rgba(24, 107, 255, 0.1);
}

.account-btn:active {
  background: linear-gradient(135deg, #bae7ff, #e8f4ff);
  transform: scale(0.95);
}
</style>