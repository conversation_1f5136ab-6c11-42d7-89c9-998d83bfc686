<template>
  <view class="container">
    <!-- 搜索栏 -->
    <view class="search-box">
      <view class="search-input">
        <image src="/assets/images/search.png" mode="widthFix" class="search-icon"></image>
        <input type="text" placeholder="搜索视频..." v-model="searchKeyword" @input="onSearchInput" />
      </view>
    </view>



    <!-- 视频列表 -->
    <view class="video-list">
      <view class="video-card" v-for="video in filteredVideos" :key="video.id">
        <view class="video-thumbnail" :style="{ backgroundImage: `url(${video.thumbnail})` }">
          <text class="video-duration">{{ video.duration }}</text>
          <text :class="[
            'video-status',
            video.status === 'published' ? 'status-published' : 
            video.status === 'failed' ? 'status-failed' :
            video.status === 'compressing' ? 'status-compressing' : 'status-draft'
          ]">
            {{ 
              video.status === "published" ? "已发布" : 
              video.status === "failed" ? "处理失败" :
              video.status === "compressing" ? "压缩中" : "草稿" 
            }}
          </text>
        </view>
        <view class="video-info">
          <text class="video-title">{{ video.title }}</text>
          <view class="video-meta">
            <text class="video-category">{{ video.category }}</text>
            <text class="video-date">{{ video.createTime }}</text>
          </view>
          <view class="video-actions">
            <view class="video-stats">
              <view class="stat-item">
                <text class="iconfont icon-play"></text>
                <text class="stat-value">{{ video.views }}</text>
              </view>
              <view class="stat-item">
                <text class="iconfont icon-chart"></text>
                <text class="stat-value">{{ video.completionRate }}%</text>
              </view>
            </view>
            <view class="action-buttons">
              <button class="action-btn edit-btn" @tap="editVideo(video.id)">
                <text class="iconfont icon-edit"></text>
              </button>
              <button class="action-btn delete-btn" @tap="confirmDelete(video.id)">
                <text class="iconfont icon-delete"></text>
              </button>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 添加按钮 -->
    <view class="add-button" @tap="addVideo">
      <text class="iconfont icon-add"></text>
    </view>

    <!-- 删除确认弹窗 -->
    <uni-popup ref="deletePopup" type="dialog">
      <uni-popup-dialog title="确认删除" content="确定要删除这个视频吗？此操作不可恢复。" :before-close="true" @confirm="deleteVideo"
        @close="cancelDelete"></uni-popup-dialog>
    </uni-popup>
  </view>
</template>

<script>
import { videoList } from "../../../mock/data.js";
import { queryVideos, deleteVideo as deleteVideoApi, toggleVideoStatus } from "@/api/video.js";

export default {
  data () {
    return {
      videos: [],
      searchKeyword: "",

      deleteVideoId: null,
      searchTimer: null, // 搜索防抖定时器
    };
  },
  computed: {
    filteredVideos () {
      let result = this.videos;

      // 搜索过滤
      if (this.searchKeyword) {
        const keyword = this.searchKeyword.toLowerCase();
        result = result.filter(
          (video) =>
            video.title.toLowerCase().includes(keyword)
        );
      }



      return result;
    },
  },
  async onLoad () {
    // 加载视频列表
    await this.loadVideos();
  },
  onShow() {
    // 页面显示时检查是否需要刷新
    this.checkAndRefresh();
  },
  onUnload() {
    // 页面卸载时移除事件监听
    uni.$off('videoDeleted');
  },
  methods: {
    // 检查并刷新数据
    checkAndRefresh() {
      // 注册事件监听
      uni.$on('videoDeleted', (data) => {
        console.log('收到视频删除事件，刷新列表', data);
        this.loadVideos();
      });
    },
    
    // 加载视频列表
    async loadVideos () {
      try {
        uni.showLoading({
          title: "加载中...",
        });

        // 构建查询参数
        const params = {
          PageIndex: 1,
          PageSize: 100, // 暂时加载所有数据，后续可以改为分页
        };

        // 如果有搜索关键词，添加到查询参数
        if (this.searchKeyword) {
          params.Title = this.searchKeyword;
        }



        const response = await queryVideos(params);

        if (response.success && response.data) {
          // 转换API数据格式为页面需要的格式
          this.videos = response.data.items.map(video => ({
            id: video.id,
            title: video.title,
            thumbnail: video.coverUrl || '/assets/images/video-cover.jpg',
            duration: this.formatDuration(video.duration),
            views: video.viewCount || 0,
            createTime: this.formatDate(video.createTime),

            status: video.status === 1 ? 'published' : 
                    video.status === 2 ? 'failed' :
                    video.status === 3 ? 'compressing' : 'draft',
            description: video.description || ''
          }));
        } else {
          throw new Error(response.msg || '获取视频列表失败');
        }

        uni.hideLoading();
      } catch (error) {
        console.error('加载视频列表失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: "加载失败",
          icon: "none",
        });

        // 失败时使用模拟数据作为fallback
        this.videos = videoList;
      }
    },

    // 格式化时长
    formatDuration (seconds) {
      if (!seconds) return '00:00';
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    },

    // 格式化日期
    formatDate (dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;
    },


    addVideo () {
      uni.showToast({
        title: "添加视频功能开发中",
        icon: "none",
      });
    },
    editVideo (videoId) {
      // 跳转到编辑页面
      uni.navigateTo({
        url: `/pages/admin/media/upload?id=${videoId}`
      });
    },
    confirmDelete (videoId) {
      this.deleteVideoId = videoId;
      this.$refs.deletePopup.open();
    },
    cancelDelete () {
      this.deleteVideoId = null;
    },
    async deleteVideo () {
      if (this.deleteVideoId) {
        try {
          uni.showLoading({
            title: "删除中...",
          });

          // 调用删除API
          const response = await deleteVideoApi(this.deleteVideoId);

          if (response.success) {
            // 删除成功，从本地列表中移除
            this.videos = this.videos.filter(
              (video) => video.id !== this.deleteVideoId
            );

            uni.showToast({
              title: "删除成功",
              icon: "success",
            });
          } else {
            throw new Error(response.msg || '删除失败');
          }

          uni.hideLoading();
          this.deleteVideoId = null;
        } catch (error) {
          console.error('删除视频失败:', error);
          uni.hideLoading();
          uni.showToast({
            title: "删除失败",
            icon: "none",
          });

          // 失败时也要关闭弹窗
          this.deleteVideoId = null;
        }
      }
    },

    // 添加搜索功能
    onSearchInput () {
      // 防抖处理，避免频繁调用API
      clearTimeout(this.searchTimer);
      this.searchTimer = setTimeout(() => {
        this.loadVideos();
      }, 500);
    },
  },
};
</script>

<style lang="scss">
@import '@/styles/index.scss';

.container {
  @extend .page-container;
  padding-bottom: 120rpx;
}

.search-box {
  padding: 20rpx;
  background-color: #fff;
}

.search-input {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 40rpx;
  padding: 10rpx 20rpx;
}

.search-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 10rpx;
}

.search-input input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
}

.category-scroll {
  background-color: #fff;
  padding: 0 20rpx 20rpx;
  white-space: nowrap;
}

.category-list {
  display: flex;
}

.category-item {
  display: inline-block;
  padding: 10rpx 30rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
  color: #333;
  background-color: #f5f5f5;
  border-radius: 40rpx;
}

.category-item.active {
  background-color: #186BFF;
  color: #fff;
}

.video-list {
  padding: 20rpx;
}

.video-card {
  margin-bottom: 20rpx;
  border-radius: 16rpx;
  background-color: #fff;
  overflow: hidden;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.video-thumbnail {
  height: 240rpx;
  background-size: cover;
  background-position: center;
  position: relative;
}

.video-duration {
  position: absolute;
  bottom: 16rpx;
  right: 16rpx;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.video-status {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.status-published {
  background-color: #e6f7ff;
  color: #186BFF;
}

.status-draft {
  background-color: #f5f5f5;
  color: #666;
}

.status-failed {
  background-color: #fff2f0;
  color: #f5222d;
}

.status-compressing {
  background-color: #fff7e6;
  color: #fa8c16;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

.video-info {
  padding: 20rpx;
}

.video-title {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.video-meta {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.video-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.video-stats {
  display: flex;
}

.stat-item {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
  font-size: 24rpx;
  color: #999;
}

.stat-item .iconfont {
  margin-right: 6rpx;
}

.action-buttons {
  display: flex;
}

.action-btn {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  padding: 0;
  font-size: 28rpx;
}

.edit-btn {
  background-color: #e6f7ff;
  color: #186BFF;
}

.delete-btn {
  background-color: #fff2f0;
  color: #f5222d;
}

.add-button {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #007AFF, #0056CC);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  box-shadow: 0 6rpx 20rpx rgba(0, 122, 255, 0.4);
  z-index: 10;
  transition: all 0.3s ease;
}

.add-button:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.3);
}
</style>