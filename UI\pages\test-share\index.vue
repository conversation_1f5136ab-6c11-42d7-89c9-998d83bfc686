<template>
  <view class="test-container">
    <view class="test-header">
      <text class="test-title">分享功能测试</text>
      <text class="test-subtitle">测试批次分享和视频播放流程</text>
    </view>

    <view class="test-section">
      <text class="section-title">1. 测试分享链接生成</text>
      <view class="test-item">
        <text class="test-label">测试批次ID:</text>
        <u-input v-model="testBatchId" placeholder="请输入批次ID" type="number" />
      </view>
      <view class="test-item">
        <text class="test-label">测试视频ID:</text>
        <u-input v-model="testVideoId" placeholder="请输入视频ID" type="number" />
      </view>
      <view class="test-item">
        <text class="test-label">分享人ID:</text>
        <u-input v-model="testSharerId" placeholder="请输入分享人ID" type="number" />
      </view>
      <u-button type="primary" @click="generateTestLink" class="test-btn">生成测试链接</u-button>

      <view v-if="generatedLink" class="generated-link">
        <text class="link-label">生成的链接:</text>
        <text class="link-text" @click="copyLink">{{ generatedLink }}</text>
        <u-button type="success" size="small" @click="testLink" class="test-link-btn">测试链接</u-button>
      </view>
    </view>

    <view class="test-section">
      <text class="section-title">2. 测试IP用户系统</text>
      <u-button type="info" @click="testIPUser" class="test-btn">测试IP用户创建</u-button>
      <u-button type="warning" @click="clearIPUserData" class="test-btn">清除IP用户数据</u-button>
    </view>

    <view class="test-section">
      <text class="section-title">3. 测试数据</text>
      <view class="test-data">
        <text class="data-label">当前用户信息:</text>
        <text class="data-text">{{ currentUserInfo }}</text>
      </view>
      <view class="test-data">
        <text class="data-label">本地存储数据:</text>
        <text class="data-text">{{ storageData }}</text>
      </view>
      <u-button type="error" @click="clearTestData" class="test-btn">清除测试数据</u-button>
    </view>

    <!-- Toast -->
    <u-toast ref="uToast" />
  </view>
</template>

<script>
import { getUIProjectUrl } from '@/utils/app-config.js'
import { getOrCreateIPUser, clearIPUserCache, getIPUserDisplayInfo } from '@/utils/ip-user.js'

export default {
  data () {
    return {
      testBatchId: '1',
      testVideoId: '1',
      testSharerId: '1',
      generatedLink: '',
      currentUserInfo: '',
      storageData: ''
    }
  },
  onLoad () {
    this.loadTestData()
  },
  onShow () {
    this.loadTestData()
  },
  methods: {
    // 生成测试链接
    generateTestLink () {
      if (!this.testBatchId || !this.testVideoId || !this.testSharerId) {
        this.showToast('请填写完整的测试参数', 'error')
        return
      }

      const UIProjectUrl = getUIProjectUrl()
      this.generatedLink = `${UIProjectUrl}/#/pages/video/index?videoId=${this.testVideoId}&batchId=${this.testBatchId}&sharerId=${this.testSharerId}`

      this.showToast('测试链接生成成功', 'success')
    },

    // 复制链接
    copyLink () {
      if (!this.generatedLink) return

      uni.setClipboardData({
        data: this.generatedLink,
        success: () => {
          this.showToast('链接已复制到剪贴板', 'success')
        },
        fail: () => {
          this.showToast('复制失败', 'error')
        }
      })
    },

    // 测试链接
    testLink () {
      if (!this.generatedLink) return

      // 解析链接参数
      const url = new URL(this.generatedLink)
      const params = new URLSearchParams(url.search)

      const videoId = params.get('videoId')
      const batchId = params.get('batchId')
      const sharerId = params.get('sharerId')

      uni.navigateTo({
        url: `/pages/video/index?videoId=${videoId}&batchId=${batchId}&sharerId=${sharerId}`
      })
    },

    // 测试IP用户创建
    async testIPUser () {
      try {
        this.showToast('正在创建IP用户...', 'loading')

        const ipUser = await getOrCreateIPUser()

        this.showToast(`IP用户创建成功: ${ipUser.userInfo.nickname}`, 'success')
        this.loadTestData() // 刷新显示数据

        console.log('IP用户详情:', ipUser)
      } catch (error) {
        console.error('IP用户创建失败:', error)
        this.showToast('IP用户创建失败', 'error')
      }
    },

    // 清除IP用户数据
    clearIPUserData () {
      uni.showModal({
        title: '确认清除',
        content: '确定要清除IP用户数据吗？',
        success: (res) => {
          if (res.confirm) {
            clearIPUserCache()
            this.showToast('IP用户数据已清除', 'success')
            this.loadTestData() // 刷新显示数据
          }
        }
      })
    },

    // 加载测试数据
    loadTestData () {
      // 获取IP用户信息
      const ipUserInfo = getIPUserDisplayInfo()
      this.currentUserInfo = ipUserInfo ? JSON.stringify(ipUserInfo, null, 2) : '无IP用户'

      // 获取所有本地存储数据
      const token = uni.getStorageSync('token')
      const sharerInfo = uni.getStorageSync('currentSharerInfo')
      const ipUser = uni.getStorageSync('ipUser')
      const deviceId = uni.getStorageSync('deviceId')

      this.storageData = JSON.stringify({
        token: token || null,
        sharerInfo: sharerInfo || null,
        ipUser: ipUser || null,
        deviceId: deviceId || null
      }, null, 2)
    },

    // 清除测试数据
    clearTestData () {
      uni.showModal({
        title: '确认清除',
        content: '确定要清除所有测试数据吗？',
        success: (res) => {
          if (res.confirm) {
            // 清除所有相关数据
            uni.removeStorageSync('userInfo')
            uni.removeStorageSync('token')
            uni.removeStorageSync('currentSharerInfo')
            clearIPUserCache() // 清除IP用户数据

            this.loadTestData()
            this.showToast('测试数据已清除', 'success')
          }
        }
      })
    },

    showToast (message, type = 'success') {
      this.$refs.uToast.show({
        message: message,
        type: type,
        duration: 2000
      })
    }
  }
}
</script>

<style scoped>
.test-container {
  padding: 30rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.test-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.test-subtitle {
  font-size: 28rpx;
  color: #666;
}

.test-section {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.test-item {
  margin-bottom: 20rpx;
}

.test-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.test-btn {
  margin-top: 20rpx;
  margin-bottom: 10rpx;
}

.generated-link {
  margin-top: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.link-label {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.link-text {
  font-size: 24rpx;
  color: #1976D2;
  word-break: break-all;
  line-height: 1.5;
  display: block;
  margin-bottom: 15rpx;
}

.test-link-btn {
  margin-top: 10rpx;
}

.test-data {
  margin-bottom: 20rpx;
}

.data-label {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.data-text {
  font-size: 24rpx;
  color: #666;
  background: #f8f9fa;
  padding: 15rpx;
  border-radius: 8rpx;
  white-space: pre-wrap;
  word-break: break-all;
  display: block;
}
</style>
