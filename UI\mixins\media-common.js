/**
 * 媒体页面公共混入
 * 包含视频状态处理、文件URL构建、日期格式化等公共方法
 */

import { formatDuration, formatDateTime, formatDate } from "@/utils/format.js";
import { API_CONFIG, ENV_TYPES } from "@/utils/config.js";

export default {
  methods: {
    /**
     * 格式化视频时长
     * @param {number} seconds - 秒数
     * @returns {string} 格式化后的时长字符串
     */
    formatDuration (seconds) {
      return formatDuration(seconds);
    },

    /**
     * 格式化日期时间
     * @param {string|Date} dateInput - 日期字符串或Date对象
     * @returns {string} 格式化后的日期时间字符串
     */
    formatDate (dateInput) {
      return formatDateTime(dateInput);
    },

    /**
     * 格式化简单日期
     * @param {string|Date} dateInput - 日期字符串或Date对象
     * @returns {string} 格式化后的日期字符串
     */
    formatSimpleDate (dateInput) {
      return formatDate(dateInput);
    },

    /**
     * 构建完整的文件URL
     * @param {string} url - 相对URL或完整URL
     * @returns {string} 完整的文件URL
     */
    buildCompleteFileUrl (url) {
      if (!url) return '';

      // 如果已经是完整的URL（包含http或https），直接返回
      if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
      }

      // 获取API基础URL并去掉/api部分
      const apiBaseUrl = API_CONFIG.BASE_URLS[ENV_TYPES.DEVELOPMENT];
      if (!apiBaseUrl || apiBaseUrl.trim() === '') {
        console.warn('API基础URL未配置，返回原始URL:', url);
        return url;
      }

      const baseUrl = apiBaseUrl.replace('/api', '');

      // 确保URL以/开头
      const normalizedUrl = url.startsWith('/') ? url : `/${url}`;

      return `${baseUrl}/wwwroot${normalizedUrl}`;
    },

    /**
     * 映射视频状态
     * @param {number} apiStatus - API返回的状态码
     * @returns {string} 页面使用的状态字符串
     */
    mapVideoStatus (apiStatus) {
      const statusMap = {
        0: 'scheduled', // 待发布
        1: 'active',    // 已上线
        2: 'expired'    // 已过期
      };
      return statusMap[apiStatus] || 'scheduled';
    },

    /**
     * 获取状态显示文本
     * @param {object} media - 媒体对象
     * @returns {string} 状态显示文本
     */
    getStatusText (media) {
      // 优先显示压缩状态
      if (media.compressionStatus !== undefined) {
        const compressionStatusMap = {
          0: '未压缩',
          1: '压缩中',
          2: '已压缩',
          3: '压缩失败'
        };
        return compressionStatusMap[media.compressionStatus] || '未知压缩状态';
      }

      // 回退到原有状态
      const statusTextMap = {
        'expired': '已过期',
        'scheduled': '待发布',
        'active': '已上线'
      };
      return statusTextMap[media.status] || '未知';
    },

    /**
     * 获取状态类型（用于UI组件）
     * @param {object} media - 媒体对象
     * @returns {string} 状态类型
     */
    getStatusType (media) {
      // 优先显示压缩状态类型
      if (media.compressionStatus !== undefined) {
        const compressionTypeMap = {
          0: 'info',      // 未压缩 - 蓝色
          1: 'warning',   // 压缩中 - 橙色
          2: 'success',   // 已压缩 - 绿色
          3: 'error'      // 压缩失败 - 红色
        };
        return compressionTypeMap[media.compressionStatus] || 'info';
      }

      // 回退到原有状态类型
      const statusTypeMap = {
        'expired': 'error',
        'scheduled': 'warning',
        'active': 'success'
      };
      return statusTypeMap[media.status] || 'default';
    },

    /**
     * 获取状态标签
     * @param {string} status - 状态字符串
     * @returns {string} 状态标签
     */
    getStatusLabel (status) {
      const statusLabelMap = {
        'all': '全部',
        'active': '已上线',
        'scheduled': '待发布',
        'expired': '已过期'
      };
      return statusLabelMap[status] || '全部';
    },

    /**
     * 显示加载提示
     * @param {string} title - 提示文本
     */
    showLoading (title = '加载中...') {
      uni.showLoading({
        title,
        mask: true
      });
    },

    /**
     * 隐藏加载提示
     */
    hideLoading () {
      uni.hideLoading();
    },

    /**
     * 显示成功提示
     * @param {string} title - 提示文本
     */
    showSuccess (title) {
      uni.showToast({
        title,
        icon: 'success',
        duration: 2000
      });
    },

    /**
     * 显示错误提示
     * @param {string} title - 提示文本
     */
    showError (title) {
      uni.showToast({
        title,
        icon: 'none',
        duration: 3000
      });
    },

    /**
     * 显示确认对话框
     * @param {string} title - 标题
     * @param {string} content - 内容
     * @returns {Promise<boolean>} 用户是否确认
     */
    showConfirm (title, content) {
      return new Promise((resolve) => {
        uni.showModal({
          title,
          content,
          success: (res) => {
            resolve(res.confirm);
          },
          fail: () => {
            resolve(false);
          }
        });
      });
    },

    /**
     * 安全的页面跳转
     * @param {string} url - 跳转URL
     * @param {object} options - 跳转选项
     */
    safeNavigateTo (url, options = {}) {
      uni.navigateTo({
        url,
        ...options,
        fail: (err) => {
          console.error('页面跳转失败:', err);
          this.showError('页面跳转失败');
        }
      });
    },

    /**
     * 安全的页面返回
     */
    safeNavigateBack () {
      uni.navigateBack({
        fail: (err) => {
          console.error('页面返回失败:', err);
          // 如果返回失败，尝试跳转到首页
          uni.reLaunch({
            url: '/pages/admin/media/index'
          });
        }
      });
    }
  }
};
