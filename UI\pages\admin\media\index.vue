<template>
    <view class="container">
        <view class="page-header">
            <!-- 头部区域 -->
            <!-- <view class="header-section">
                <view class="title-area">
                    <text class="page-title">媒体素材库</text>
                </view>
            </view> -->

            <!-- 主选项卡 -->
            <view class="main-tabs">
                <view :class="['main-tab', contentType === 'videos' ? 'active' : '']"
                    @tap="switchContentType('videos')">
                    视频
                </view>
                <view :class="['main-tab', contentType === 'batches' ? 'active' : '']"
                    @tap="switchContentType('batches')">
                    批次
                </view>
            </view>
        </view>

        <!-- 内容区域 -->
        <view class="content-container">
            <!-- 视频列表组件 -->
            <videos-list v-if="contentType === 'videos'"></videos-list>

            <!-- 批次列表组件 -->
            <batches-list v-if="contentType === 'batches'"></batches-list>
        </view>
    </view>
</template>

<script>
import VideosList from './videos-list.vue';
import BatchesList from './batches-list.vue';

export default {
    components: {
        VideosList,
        BatchesList
    },
    data () {
        return {
            contentType: 'videos', // 'videos' 或 'batches'
        }
    },
    methods: {
        switchContentType (type) {
            this.contentType = type;
        }
    }
}
</script>

<style>
.container {
    padding: 0;
    background-color: #f7f7f7;
}

.page-header {
    position: fixed;
    width: 100%;
    z-index: 999;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
    background-color: #fff;
}

/* 头部区域 */
.header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10rpx 30rpx;
    background-color: #fff;
}

.title-area {
    flex: 1;
    display: flex;
    align-items: center;
}

.page-title {
    font-size: 34rpx;
    color: #333;
}

/* 主选项卡 */
.main-tabs {
    display: flex;
    background-color: #fff;
    border-bottom: 1rpx solid #eaeaea;
}

.main-tab {
    flex: 1;
    text-align: center;
    padding: 20rpx 0;
    font-size: 30rpx;
    color: #666;
    position: relative;
    transition: all 0.3s ease;
}

.main-tab.active {
    color: #007AFF;
    font-weight: 500;
}

.main-tab.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 25%;
    width: 50%;
    height: 4rpx;
    background-color: #007AFF;
    border-radius: 2rpx;
}

/* 内容区域 */
.content-container {
    padding-top: 2.55rem;
    /* 头部高度 */
}
</style>