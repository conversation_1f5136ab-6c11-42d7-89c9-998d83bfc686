<template>
  <view class="video-uploader">


    <!-- 视频上传区域 -->
    <view class="upload-container">
      <!-- 现代化上传区域 -->
      <view class="upload-area" @tap="chooseVideo" :class="{ disabled: isEditMode, analyzing: isAnalyzing }">
        <view class="upload-placeholder" v-if="!videoInfo.path">
          <view class="upload-icon-container">
            <text class="upload-icon" :class="{ rotating: isAnalyzing }">🎬</text>
          </view>
          <text class="upload-text">{{
            isAnalyzing ? "正在分析视频..." :
              isEditMode ? "视频不可更换" : "拖拽或点击上传视频"
          }}</text>
          <text class="upload-desc">{{
            isEditMode
              ? "编辑模式下只能修改视频信息"
              : "支持 MP4、MOV、AVI、MKV 格式，最大 2GB"
          }}</text>
          <view class="upload-features" v-if="!isEditMode">
            <view class="feature-item">
              <text class="feature-icon">⚡</text>
              <text class="feature-text">快速上传</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">🎯</text>
              <text class="feature-text">智能分析</text>
            </view>
            <view class="feature-item">
              <text class="feature-icon">🔒</text>
              <text class="feature-text">安全存储</text>
            </view>
          </view>
        </view>
        <view class="upload-preview" v-else>
          <image :src="videoInfo.thumbnail" mode="aspectFill"></image>
          <text class="video-duration">{{ videoInfo.duration }}</text>
          <view class="preview-actions" @tap.stop>
            <button type="button" class="action-btn" @tap.stop="chooseVideo" v-if="!isEditMode">
              <text class="iconfont icon-refresh"></text>
              重新选择
            </button>
          </view>
          <view class="preview-mask edit-mode" v-if="isEditMode">
            <text class="preview-text">视频不可更换</text>
          </view>
        </view>
      </view>

      <!-- 高级设置（视频压缩选项） -->
      <view class="advanced-settings" v-if="videoInfo.path && !isEditMode">
        <view class="settings-header" @tap="toggleAdvancedSettings">
          <text class="settings-title">高级设置</text>
          <text class="settings-toggle">{{ showAdvancedSettings ? '收起' : '展开' }}</text>
          <text class="settings-icon">{{ showAdvancedSettings ? '▲' : '▼' }}</text>
        </view>

        <!-- 智能压缩设置 -->
        <view class="compression-panel" v-if="showAdvancedSettings">
          <view class="panel-header">
            <text class="panel-title">🎯 智能压缩</text>
            <text class="panel-desc">上传后自动压缩，节省存储空间和传输时间</text>
          </view>

          <!-- 压缩开关 -->
          <view class="compression-toggle">
            <view class="toggle-item">
              <text class="toggle-label">启用压缩</text>
              <switch :checked="enableCompression" @change="onCompressionToggle" color="#186BFF" />
            </view>
            <text class="toggle-desc">开启后将在服务器端自动压缩视频</text>
          </view>

          <!-- 压缩质量选择 -->
          <view class="compression-quality" v-if="enableCompression">
            <text class="quality-label">压缩质量</text>
            <view class="quality-options">
              <view class="quality-option" :class="{ active: selectedQuality === option.value }"
                v-for="option in qualityOptions" :key="option.value" @tap="selectQuality(option.value)">
                <view class="option-icon">{{ option.icon }}</view>
                <view class="option-content">
                  <text class="option-title">{{ option.title }}</text>
                  <text class="option-desc">{{ option.description }}</text>
                </view>
                <view class="option-badge" v-if="option.recommended">推荐</view>
              </view>
            </view>
          </view>

          <!-- 压缩说明 -->
          <view class="compression-info" v-if="enableCompression">
            <view class="info-item">
              <text class="info-icon">⚡</text>
              <text class="info-text">压缩将在上传完成后自动开始</text>
            </view>
            <view class="info-item">
              <text class="info-icon">📱</text>
              <text class="info-text">您可以关闭页面，压缩在后台进行</text>
            </view>
            <view class="info-item">
              <text class="info-icon">🔔</text>
              <text class="info-text">压缩完成后可在视频管理中查看</text>
            </view>
          </view>
        </view>
      </view>





      <!-- 视频信息面板 -->
      <view class="video-info-panel" v-if="videoInfo.path">
        <view class="panel-header">
          <text class="panel-title">视频信息</text>
          <text class="panel-status success">✓ 分析完成</text>
        </view>
        <view class="info-grid">
          <view class="info-item">
            <text class="info-label">文件大小</text>
            <text class="info-value">{{ formatFileSize(videoInfo.fileSize) }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">视频时长</text>
            <text class="info-value">{{ videoInfo.duration }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">比特率</text>
            <text class="info-value">{{ videoInfo.bitrate || '计算中...' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">状态</text>
            <text class="info-value status-good">✓ 符合要求</text>
          </view>
        </view>
      </view>

      <!-- 现代化封面上传区域 -->
      <view class="cover-upload-section" v-if="videoInfo.path && !isEditMode">
        <view class="section-header">
          <text class="section-title">视频封面 <text class="required-mark">*</text></text>
          <text class="section-desc">请为你的视频选择一个吸引人的封面（必填）</text>
        </view>

        <!-- 统一的封面上传区域 -->
        <view class="cover-upload-area" @tap="chooseCoverImage">
          <view class="cover-content" v-if="videoInfo.thumbnail">
            <!-- 已有封面时显示预览 -->
            <image :src="videoInfo.thumbnail" mode="aspectFill" class="cover-preview-image"></image>
            <view class="cover-overlay">
              <view class="overlay-content">
                <text class="overlay-icon">📷</text>
                <text class="overlay-text">点击更换封面</text>
              </view>
            </view>
            <view class="cover-status-badge">
              <text class="status-text">✓ 已设置</text>
            </view>
          </view>

          <view class="cover-placeholder" v-else>
            <!-- 未设置封面时显示上传提示 -->
            <view class="placeholder-icon-container">
              <text class="placeholder-icon">🖼️</text>
            </view>
            <text class="placeholder-title">选择封面图片</text>
            <text class="placeholder-subtitle">点击上传或拖拽图片到此处</text>
            <view class="placeholder-features">
              <text class="feature-text">• 支持 JPG、PNG 格式</text>
              <text class="feature-text">• 建议 16:9 比例</text>
              <text class="feature-text">• 文件不超过 5MB</text>
            </view>
          </view>
        </view>
      </view>
    </view>




  </view>
</template>

<script>
export default {
  name: 'VideoUploader',
  props: {
    value: {
      type: Object,
      default: () => ({
        path: "",
        thumbnail: "",
        duration: "",
        fileSize: 0,
        bitrate: "",
        title: "",
      })
    },
    isEditMode: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      videoInfo: { ...this.value },
      isAnalyzing: false,
      showAdvancedSettings: false, // 控制高级设置的显示/隐藏
      enableCompression: this.value.enableCompression !== false, // 默认启用压缩
      selectedQuality: this.value.compressionQuality || 7, // 默认质量等级 (1-10)
      qualityOptions: [
        {
          value: 10,
          title: '超高画质',
          description: '几乎无损压缩，适合专业用途',
          icon: '💎',
          recommended: false
        },
        {
          value: 8,
          title: '高画质',
          description: '轻微压缩，画质优秀',
          icon: '🌟',
          recommended: false
        },
        {
          value: 7,
          title: '标准画质',
          description: '平衡画质与文件大小',
          icon: '⚖️',
          recommended: true
        },
        {
          value: 5,
          title: '经济画质',
          description: '适度压缩，节省空间',
          icon: '💰',
          recommended: false
        },
        {
          value: 3,
          title: '极简画质',
          description: '最大压缩，快速传输',
          icon: '🚀',
          recommended: false
        }
      ]
    }
  },

  watch: {
    value: {
      handler (newVal) {
        // 只更新父组件传递的特定字段，保持组件内部的视频相关数据
        if (newVal) {
          this.videoInfo = {
            ...this.videoInfo, // 保持组件内部的数据
            ...newVal, // 覆盖父组件传递的数据
            // 但如果组件内部有视频路径而父组件没有，保持组件内部的
            path: this.videoInfo.path || newVal.path || '',
            thumbnail: this.videoInfo.thumbnail || newVal.thumbnail || '',
            duration: this.videoInfo.duration || newVal.duration || '',
            fileSize: this.videoInfo.fileSize || newVal.fileSize || 0
          };
          // 同步压缩设置
          this.enableCompression = newVal.enableCompression !== false;
          this.selectedQuality = newVal.compressionQuality || 7;
        }
      },
      deep: true
    },

  },
  methods: {
    chooseVideo () {
      if (this.isEditMode) {
        uni.showToast({
          title: "编辑模式下不能更换视频",
          icon: "none",
        });
        return;
      }

      uni.chooseVideo({
        count: 1,
        sourceType: ["album", "camera"],
        maxDuration: 600,
        success: (res) => {
          if (res && res.tempFilePath) {
            this.analyzeVideo(res);
          } else {
            console.log('用户取消选择视频');
          }
        },
        fail: (err) => {
          console.error('选择视频失败:', err);
          uni.showToast({
            title: "选择视频失败",
            icon: "none",
          });
        }
      });
    },

    async analyzeVideo (videoRes) {
      this.isAnalyzing = true;

      try {
        // 验证视频数据
        if (!videoRes || !videoRes.tempFilePath) {
          throw new Error('无效的视频数据');
        }

        uni.showLoading({
          title: "处理视频中...",
          mask: true
        });

        this.videoInfo.path = videoRes.tempFilePath || '';
        this.videoInfo.duration = this.formatDuration(videoRes.duration || 0);
        this.videoInfo.fileSize = (videoRes.size !== undefined) ? videoRes.size : 0;

        // 不自动设置标题，让用户手动输入

        this.processBasicVideoInfo(videoRes);
        this.setVideoThumbnail(videoRes);
        this.validateVideoBasic();

        // 手动触发更新，只更新视频相关字段，保留用户输入的其他信息
        this.$emit('input', {
          ...this.value, // 保留父组件的所有数据
          path: this.videoInfo.path,
          thumbnail: this.videoInfo.thumbnail,
          duration: this.videoInfo.duration,
          fileSize: this.videoInfo.fileSize,
          resolution: this.videoInfo.resolution,
          format: this.videoInfo.format,
          bitrate: this.videoInfo.bitrate,
          fps: this.videoInfo.fps
        });

        await new Promise(resolve => setTimeout(resolve, 500));

        uni.hideLoading();
        uni.showToast({
          title: "视频处理完成",
          icon: "success"
        });

      } catch (error) {
        console.error('视频处理失败:', error);
        uni.hideLoading();
        uni.showToast({
          title: "视频处理失败",
          icon: "none"
        });
      } finally {
        this.isAnalyzing = false;
      }
    },

    processBasicVideoInfo (videoRes) {
      if (videoRes.duration && videoRes.duration > 0 && videoRes.size) {
        const bitrate = Math.round((videoRes.size * 8) / videoRes.duration / 1000);
        this.videoInfo.bitrate = `${bitrate} kbps`;
      } else {
        this.videoInfo.bitrate = "未知";
      }
    },

    setVideoThumbnail (videoRes) {
      if (videoRes.thumbTempFilePath) {
        // 如果系统提供了缩略图，可以作为默认选项，但用户仍需确认或重新选择
        this.videoInfo.thumbnail = videoRes.thumbTempFilePath;
      } else {
        // 不自动生成默认封面，要求用户手动选择
        this.videoInfo.thumbnail = '';
      }
    },









    useDefaultBase64Cover () {
      // 使用Canvas生成占位图，避免btoa中文字符问题
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        canvas.width = 320;
        canvas.height = 180;

        // 创建渐变背景
        const gradient = ctx.createLinearGradient(0, 0, 320, 180);
        gradient.addColorStop(0, '#667eea');
        gradient.addColorStop(1, '#764ba2');
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 320, 180);

        // 绘制播放按钮
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.beginPath();
        ctx.moveTo(120, 60);
        ctx.lineTo(120, 120);
        ctx.lineTo(180, 90);
        ctx.closePath();
        ctx.fill();

        // 绘制文字（使用英文避免编码问题）
        ctx.fillStyle = 'white';
        ctx.font = '16px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('Video Cover', 160, 150);
        ctx.font = '12px Arial';
        ctx.fillText(`Duration: ${this.videoInfo.duration}`, 160, 170);

        this.videoInfo.thumbnail = canvas.toDataURL('image/jpeg', 0.8);
      } catch (error) {
        console.error('生成默认封面失败:', error);
        // 最后的备选方案：使用简单的data URL
        this.videoInfo.thumbnail = 'data:image/svg+xml;charset=utf-8,' + encodeURIComponent(`
          <svg width="320" height="180" xmlns="http://www.w3.org/2000/svg">
            <rect width="320" height="180" fill="#667eea"/>
            <polygon points="120,60 120,120 180,90" fill="white" opacity="0.8"/>
          </svg>
        `);
      }
    },



    validateVideoBasic () {
      const maxSize = 2 * 1024 * 1024 * 1024; // 2GB

      if (this.videoInfo.fileSize > maxSize) {
        uni.showToast({
          title: `文件过大(${this.formatFileSize(this.videoInfo.fileSize)})，最大支持2GB`,
          icon: "none",
          duration: 3000
        });
      }
    },



    formatFileSize (bytes) {
      if (bytes === 0) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    formatDuration (seconds) {
      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins}:${secs.toString().padStart(2, '0')}`;
    },





















    chooseCoverImage () {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.videoInfo.thumbnail = res.tempFilePaths[0];
          // 触发父组件更新，只更新封面字段
          this.$emit('input', {
            ...this.value, // 保留父组件的所有数据
            thumbnail: this.videoInfo.thumbnail
          });
          uni.showToast({
            title: "封面设置成功",
            icon: "success"
          });
        },
        fail: (err) => {
          console.error('选择图片失败:', err);
          uni.showToast({
            title: "选择图片失败",
            icon: "none"
          });
        }
      });
    },

    // 高级设置切换方法
    toggleAdvancedSettings () {
      this.showAdvancedSettings = !this.showAdvancedSettings;
    },

    // 压缩相关方法
    onCompressionToggle (event) {
      this.enableCompression = event.detail.value;
      console.log('压缩开关:', this.enableCompression);
      // 触发父组件更新
      this.updateVideoInfo();
    },

    selectQuality (value) {
      this.selectedQuality = value;
      console.log('选择压缩质量:', value);
      // 触发父组件更新
      this.updateVideoInfo();
    },

    // 更新视频信息并通知父组件
    updateVideoInfo () {
      const updatedInfo = {
        ...this.value, // 保留父组件的所有数据
        enableCompression: this.enableCompression,
        compressionQuality: this.selectedQuality
      };
      this.$emit('input', updatedInfo);
    },

    // 获取压缩设置的描述文本
    getCompressionDescription () {
      if (!this.enableCompression) {
        return '不压缩，保持原始文件';
      }
      const quality = this.qualityOptions.find(q => q.value === this.selectedQuality);
      return quality ? quality.description : '标准压缩';
    },


  }
}
</script>

<style lang="scss" scoped>
/* 如果样式变量文件不存在，使用内联变量 */
$spacing-xl: 40rpx;
$spacing-lg: 32rpx;
$spacing-md: 24rpx;
$spacing-sm: 16rpx;
$spacing-xs: 8rpx;
$border-radius-md: 12rpx;
$border-radius-lg: 16rpx;
$border-radius-sm: 8rpx;
$border-radius-base: 8rpx;
$font-size-lg: 32rpx;
$font-size-base: 28rpx;
$font-size-sm: 24rpx;
$font-weight-bold: 600;
$font-weight-medium: 500;
$text-primary: #333333;
$text-secondary: #666666;
$text-tertiary: #999999;
$text-disabled: #cccccc;
$text-white: #ffffff;
$bg-primary: #ffffff;
$bg-secondary: #f8f9fa;
$bg-tertiary: #f5f5f5;
$border-primary: #e9ecef;
$primary-color: #186BFF;
$transition-base: 0.3s ease;

/* 视频上传容器 */
.upload-container {
  margin-bottom: 40rpx;
}

/* 现代化视频上传区域 */
.upload-area {
  min-height: 400rpx;
  background: linear-gradient(135deg, white 0%, #f8fafc 100%);
  border: 3rpx dashed #cbd5e1;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  cursor: pointer;
}

.upload-area:hover {
  border-color: #186BFF;
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f2ff 100%);
  transform: translateY(-4rpx);
  box-shadow: 0 20rpx 60rpx rgba(24, 107, 255, 0.15);
}

.upload-area.disabled {
  cursor: not-allowed;
  opacity: 0.6;
  filter: grayscale(0.5);

  &:hover {
    transform: none;
    box-shadow: none;
  }
}

.upload-area.analyzing {
  border-color: #186BFF;
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f2ff 100%);
  box-shadow: 0 0 0 6rpx rgba(24, 107, 255, 0.1);
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-align: center;
  padding: 40rpx;
}

.upload-icon-container {
  margin-bottom: 32rpx;
}

.upload-icon {
  font-size: 120rpx;
  color: #94a3b8;
  display: block;
  margin-bottom: 16rpx;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));
}

.upload-icon.rotating {
  animation: modernRotate 2s linear infinite;
  color: #186BFF;
}

@keyframes modernRotate {
  0% {
    transform: rotate(0deg) scale(1);
  }

  50% {
    transform: rotate(180deg) scale(1.1);
  }

  100% {
    transform: rotate(360deg) scale(1);
  }
}

.upload-text {
  font-size: 36rpx;
  color: #334155;
  margin-bottom: 16rpx;
  font-weight: 600;
  line-height: 1.4;
}

.upload-desc {
  font-size: 28rpx;
  color: #64748b;
  margin-bottom: 32rpx;
  line-height: 1.5;
}

.upload-features {
  display: flex;
  gap: 32rpx;
  margin-top: 24rpx;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.feature-icon {
  font-size: 32rpx;
}

.feature-text {
  font-size: 20rpx;
  color: #64748b;
  font-weight: 500;
}

.upload-preview {
  position: relative;
  width: 100%;
  height: 100%;
}

.upload-preview image {
  width: 100%;
  height: 100%;
}

.video-duration {
  position: absolute;
  bottom: $spacing-sm;
  right: $spacing-sm;
  background: rgba(0, 0, 0, 0.7);
  color: $text-white;
  padding: 4rpx 8rpx;
  border-radius: $border-radius-sm;
  font-size: $font-size-sm;
}

.preview-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all $transition-base;
}

.preview-mask.edit-mode {
  background: rgba(0, 0, 0, 0.7);
}

.preview-text {
  color: $text-white;
  font-size: $font-size-base;
}

/* 预览操作按钮 */
.preview-actions {
  position: absolute;
  bottom: $spacing-md;
  left: $spacing-md;
  right: $spacing-md;
  display: flex;
  gap: $spacing-sm;
}

.action-btn {
  flex: 1;
  height: 60rpx;
  background: rgba(0, 0, 0, 0.7);
  color: $text-white;
  border: none;
  border-radius: $border-radius-sm;
  font-size: $font-size-sm;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: $spacing-xs;
}

.action-btn .iconfont {
  font-size: $font-size-sm;
}

/* 高级设置面板 */
.advanced-settings {
  margin-bottom: 24rpx;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 32rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1rpx solid #dee2e6;
  border-radius: 12rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.settings-header:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

.settings-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #495057;
}

.settings-toggle {
  font-size: 24rpx;
  color: #6c757d;
}

.settings-icon {
  font-size: 20rpx;
  color: #6c757d;
  margin-left: 8rpx;
}

/* 智能压缩面板 */
.compression-panel {
  background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 100%);
  border: 2rpx solid #186BFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-top: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(24, 107, 255, 0.1);
}

.compression-panel .panel-header {
  text-align: center;
  margin-bottom: 32rpx;
}

.compression-panel .panel-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #186BFF;
  margin-bottom: 8rpx;
}

.compression-panel .panel-desc {
  font-size: 26rpx;
  color: #64748b;
  line-height: 1.5;
}

/* 压缩开关 */
.compression-toggle {
  background: white;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.toggle-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.toggle-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #1e293b;
}

.toggle-desc {
  font-size: 24rpx;
  color: #64748b;
  line-height: 1.4;
}

/* 压缩质量选择 */
.compression-quality {
  background: white;
  border-radius: 12rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.quality-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 20rpx;
  display: block;
}

.quality-options {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.quality-option {
  display: flex;
  align-items: center;
  padding: 20rpx;
  border: 2rpx solid #e2e8f0;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  position: relative;
}

.quality-option.active {
  border-color: #186BFF;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(24, 107, 255, 0.2);
}

.option-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  width: 48rpx;
  text-align: center;
}

.option-content {
  flex: 1;
}

.option-title {
  font-size: 26rpx;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4rpx;
  display: block;
}

.option-desc {
  font-size: 22rpx;
  color: #64748b;
  line-height: 1.3;
}

.option-badge {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  font-size: 20rpx;
  font-weight: bold;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  box-shadow: 0 2rpx 8rpx rgba(245, 158, 11, 0.3);
}

/* 压缩说明 */
.compression-info {
  background: linear-gradient(135deg, #fefce8 0%, #ffffff 100%);
  border: 1rpx solid #fbbf24;
  border-radius: 12rpx;
  padding: 24rpx;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-icon {
  font-size: 24rpx;
  margin-right: 12rpx;
  width: 32rpx;
  text-align: center;
}

.info-text {
  font-size: 24rpx;
  color: #92400e;
  line-height: 1.4;
}

.compression-option {
  background: #ffffff;
  border: 2rpx solid #e9ecef;
  border-radius: 8rpx;
  padding: 20rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}

.compression-option.active {
  border-color: #007aff;
  background: #f0f8ff;
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.2);
}

.option-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.option-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.option-quality {
  font-size: 24rpx;
  color: #007aff;
  font-weight: 500;
}

.option-desc {
  font-size: 24rpx;
  color: #666666;
  margin-bottom: 8rpx;
}

.option-size {
  font-size: 22rpx;
  color: #999999;
}

.compression-actions {
  margin-top: 24rpx;
  text-align: center;
}

.compress-btn {
  background: linear-gradient(135deg, #007aff 0%, #0056cc 100%);
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.compress-btn:disabled {
  background: #cccccc;
  cursor: not-allowed;
}

/* 视频信息面板 */
.video-info-panel {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

/* 现代化封面上传区域 */
.cover-upload-section {
  margin-top: 32rpx;
  background: linear-gradient(135deg, #f0f8ff 0%, #ffffff 100%);
  border: 2rpx solid #e6f2ff;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(24, 107, 255, 0.08);
}

.section-header {
  margin-bottom: 24rpx;
  text-align: center;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
  display: block;
}

.required-mark {
  color: #ff4757;
  font-weight: bold;
}

.section-desc {
  font-size: 24rpx;
  color: #666666;
  display: block;
}

/* 统一的封面上传区域 */
.cover-upload-area {
  width: 100%;
  min-height: 240rpx;
  border: 3rpx dashed #cbd5e1;
  border-radius: 16rpx;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: white;

  &:hover {
    border-color: #186BFF;
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f2ff 100%);
    transform: translateY(-2rpx);
    box-shadow: 0 12rpx 32rpx rgba(24, 107, 255, 0.15);
  }
}

.cover-content {
  position: relative;
  width: 100%;
  height: 240rpx;
}

.cover-preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.cover-upload-area:hover .cover-overlay {
  opacity: 1;
}

.overlay-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
  color: white;
}

.overlay-icon {
  font-size: 48rpx;
}

.overlay-text {
  font-size: 28rpx;
  font-weight: 600;
}

.cover-status-badge {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background: rgba(24, 107, 255, 0.9);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 600;
  backdrop-filter: blur(8rpx);
}

.cover-placeholder {
  width: 100%;
  height: 240rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 32rpx;
}

.placeholder-icon-container {
  margin-bottom: 24rpx;
}

.placeholder-icon {
  font-size: 80rpx;
  color: #94a3b8;
  filter: drop-shadow(0 4rpx 8rpx rgba(0, 0, 0, 0.1));
}

.placeholder-title {
  font-size: 32rpx;
  color: #334155;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.placeholder-subtitle {
  font-size: 24rpx;
  color: #64748b;
  margin-bottom: 24rpx;
}

.placeholder-features {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.feature-text {
  font-size: 20rpx;
  color: #64748b;
}

/* 删除旧的上传按钮样式，现在使用统一的点击区域 */

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-md;
  padding-bottom: $spacing-md;
  border-bottom: 1rpx solid #e9ecef;
}

.panel-title {
  font-size: $font-size-lg;
  font-weight: $font-weight-bold;
  color: $text-primary;
}

.panel-status {
  font-size: $font-size-sm;
  padding: 4rpx 12rpx;
  border-radius: $border-radius-sm;
  font-weight: $font-weight-medium;
}

.panel-status.success {
  background: #d4edda;
  color: #155724;
  border: 1rpx solid #c3e6cb;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: $spacing-md;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
}

.info-label {
  font-size: $font-size-sm;
  color: $text-tertiary;
  font-weight: $font-weight-medium;
}

.info-value {
  font-size: $font-size-base;
  color: $text-primary;
  font-weight: $font-weight-medium;
}

.info-value.status-good {
  color: #28a745;
}



.btn {
  padding: $spacing-md $spacing-lg;
  border-radius: $border-radius-base;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  border: none;
  cursor: pointer;
  transition: all $transition-base;
}

.btn-primary {
  background: $primary-color;
  color: $text-white;
}

.btn-secondary {
  background: $bg-tertiary;
  color: $text-secondary;
  border: 1rpx solid $border-primary;
}
</style>
